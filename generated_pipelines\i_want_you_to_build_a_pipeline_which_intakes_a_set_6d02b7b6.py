"""
Generated Data Pipeline
Created by Multi-Agent Pipeline System
Generated on: 2025-06-02 02:26:33

This pipeline was automatically generated based on your requirements.
Please ensure you have all required API keys and dependencies installed.
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from prefect import task, flow
import json

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Configuration
CONFIG = {
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "max_results": 10,
    "temperature": 0.7,

}

def load_config():
    """Load configuration from environment variables or config file."""
    config = CONFIG.copy()
    
    # Override with environment variables if available
    for key in config:
        env_key = key.upper()
        if env_key in os.environ:
            config[key] = os.environ[env_key]
    
    return config


# Component Functions

# CHUNKING COMPONENT
from prefect import task, flow
from typing import List, Dict, Any, Optional, Tuple, Callable
import re
import tiktoken
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@task
def load_document(file_path: str) -> str:
    """Load document content from a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        logger.info(f"Successfully loaded document from {file_path}")
        return content
    except Exception as e:
        logger.error(f"Error loading document: {e}")
        raise

@task
def count_tokens(text: str, encoding_name: str = "cl100k_base") -> int:
    """Count the number of tokens in the text using tiktoken."""
    try:
        encoding = tiktoken.get_encoding(encoding_name)
        tokens = encoding.encode(text)
        return len(tokens)
    except Exception as e:
        logger.error(f"Error counting tokens: {e}")
        # Fallback to approximate counting if tiktoken fails
        return len(text.split()) * 1.3  # Rough approximation

# Helper functions for different types of splitting
def split_by_headings(text: str, heading_pattern: str = r'^#{1,6}\s+.+$', min_heading_length: int = 3) -> List[Dict[str, Any]]:
    """Split text by Markdown headings.

    Args:
        text: The text to split
        heading_pattern: Regex pattern to identify headings
        min_heading_length: Minimum length of heading text to be considered valid

    Returns:
        List of dictionaries with heading and content
    """
    # Find all headings and their positions
    heading_matches = list(re.finditer(heading_pattern, text, re.MULTILINE))

    if not heading_matches:
        return [{
            "heading": "",
            "content": text,
            "level": 0
        }]

    sections = []

    # Process each heading and its content
    for i, match in enumerate(heading_matches):
        heading = match.group(0).strip()
        heading_level = len(re.match(r'^(#+)', heading).group(1))  # Count # symbols for level
        heading_text = re.sub(r'^#+\s+', '', heading)  # Remove # symbols from heading text

        # Skip headings that are too short (likely false positives)
        if len(heading_text) < min_heading_length:
            continue

        # Get the content for this section (until the next heading or end of text)
        start_pos = match.end()
        end_pos = heading_matches[i+1].start() if i < len(heading_matches) - 1 else len(text)
        content = text[start_pos:end_pos].strip()

        sections.append({
            "heading": heading_text,
            "content": content,
            "level": heading_level
        })

    # If there's content before the first heading, add it as an intro section
    if heading_matches[0].start() > 0:
        intro_content = text[:heading_matches[0].start()].strip()
        if intro_content:
            sections.insert(0, {
                "heading": "Introduction",
                "content": intro_content,
                "level": 0
            })

    return sections

def split_by_structure(text: str, structure_type: str = "markdown") -> List[Dict[str, Any]]:
    """Split text based on document structure.

    Args:
        text: The text to split
        structure_type: Type of document structure (markdown, html, etc.)

    Returns:
        List of sections with metadata
    """
    if structure_type.lower() == "markdown":
        return split_by_headings(text)
    else:
        # Default to simple paragraph splitting if structure type is not recognized
        paragraphs = re.split(r'\n\s*\n', text)
        return [{
            "heading": "",
            "content": p.strip(),
            "level": 0
        } for p in paragraphs if p.strip()]

def estimate_complexity(text: str) -> float:
    """Estimate the complexity of text based on simple heuristics.

    Higher values indicate more complex text that might need smaller chunks.

    Args:
        text: The text to analyze

    Returns:
        A complexity score between 0 and 1
    """
    # This is a simplified complexity estimation
    # In a real implementation, you might use more sophisticated metrics

    # Count sentences, words, and average word length
    sentences = re.split(r'[.!?]\s+', text)
    words = text.split()

    if not words:
        return 0.0

    avg_word_length = sum(len(word) for word in words) / len(words)
    avg_sentence_length = len(words) / max(1, len(sentences))

    # Normalize metrics to 0-1 range (these thresholds are arbitrary)
    normalized_word_length = min(1.0, avg_word_length / 10.0)  # Assume max avg word length is 10
    normalized_sentence_length = min(1.0, avg_sentence_length / 30.0)  # Assume max avg sentence length is 30

    # Calculate complexity score (equal weighting)
    complexity = (normalized_word_length + normalized_sentence_length) / 2.0

    return complexity

@task
def smart_adaptive_chunking(
    text: str,
    base_chunk_size: int = 1000,
    min_chunk_size: int = 100,
    max_chunk_size: int = 2000,
    chunk_overlap: int = 200,
    structure_type: str = "markdown",
    encoding_name: str = "cl100k_base"
) -> List[Dict[str, Any]]:
    """Adaptively split text into chunks based on structure and content complexity.

    Args:
        text: The text to split into chunks
        base_chunk_size: Base size of a chunk (in tokens)
        min_chunk_size: Minimum size of a chunk (in tokens)
        max_chunk_size: Maximum size of a chunk (in tokens)
        chunk_overlap: The number of tokens to overlap between chunks
        structure_type: Type of document structure (markdown, html, etc.)
        encoding_name: The name of the tiktoken encoding to use

    Returns:
        A list of dictionaries, each containing a chunk of text and metadata
    """
    # Initialize token counter
    try:
        encoding = tiktoken.get_encoding(encoding_name)
        def token_counter(s):
            return len(encoding.encode(s))
    except Exception as e:
        logger.error(f"Error initializing tokenizer: {e}")
        # Fallback to simple word count approximation
        def token_counter(s):
            return len(s.split()) * 1.3  # Rough approximation

    # Split text based on document structure
    sections = split_by_structure(text, structure_type)
    logger.info(f"Split document into {len(sections)} structural sections")

    chunks = []
    current_chunk_text = ""
    current_chunk_tokens = 0
    current_chunk_sections = []

    for section in sections:
        section_text = section["content"]
        section_heading = section["heading"]
        section_level = section["level"]

        # Skip empty sections
        if not section_text.strip():
            continue

        # Estimate section complexity to adjust chunk size
        complexity = estimate_complexity(section_text)

        # Adjust target chunk size based on complexity
        # More complex content gets smaller chunks
        adjusted_chunk_size = int(base_chunk_size * (1.0 - 0.5 * complexity))
        adjusted_chunk_size = max(min_chunk_size, min(max_chunk_size, adjusted_chunk_size))

        section_tokens = token_counter(section_text)

        # If section is too large, split it further
        if section_tokens > adjusted_chunk_size:
            # Finalize current chunk if not empty
            if current_chunk_text:
                chunks.append({
                    "text": current_chunk_text,
                    "metadata": {
                        "token_count": current_chunk_tokens,
                        "sections": current_chunk_sections,
                        "chunk_type": "adaptive"
                    }
                })
                current_chunk_text = ""
                current_chunk_tokens = 0
                current_chunk_sections = []

            # Split large section into paragraphs
            paragraphs = re.split(r'\n\s*\n', section_text)

            # Process each paragraph
            para_chunk_text = ""
            para_chunk_tokens = 0
            para_chunk_sections = []

            for para in paragraphs:
                para_tokens = token_counter(para)

                # If adding this paragraph would exceed the adjusted chunk size
                if para_chunk_tokens + para_tokens > adjusted_chunk_size and para_chunk_text:
                    # Add section heading to the chunk if it's the first paragraph
                    full_chunk_text = para_chunk_text
                    if section_heading and para_chunk_sections == []:
                        full_chunk_text = f"# {section_heading}\n\n{full_chunk_text}"

                    chunks.append({
                        "text": full_chunk_text,
                        "metadata": {
                            "token_count": para_chunk_tokens,
                            "sections": para_chunk_sections + [section_heading],
                            "chunk_type": "adaptive_paragraph"
                        }
                    })

                    # Start a new paragraph chunk
                    para_chunk_text = para
                    para_chunk_tokens = para_tokens
                    para_chunk_sections = []
                else:
                    # Add paragraph to current chunk
                    if para_chunk_text:
                        para_chunk_text += "\n\n" + para
                    else:
                        para_chunk_text = para
                    para_chunk_tokens += para_tokens

            # Add the last paragraph chunk if not empty
            if para_chunk_text:
                # Add section heading to the chunk if it's the first paragraph
                full_chunk_text = para_chunk_text
                if section_heading and para_chunk_sections == []:
                    full_chunk_text = f"# {section_heading}\n\n{full_chunk_text}"

                chunks.append({
                    "text": full_chunk_text,
                    "metadata": {
                        "token_count": para_chunk_tokens,
                        "sections": para_chunk_sections + [section_heading],
                        "chunk_type": "adaptive_paragraph"
                    }
                })
        else:
            # If adding this section would exceed the max chunk size
            if current_chunk_tokens + section_tokens > max_chunk_size and current_chunk_text:
                # Finalize current chunk
                chunks.append({
                    "text": current_chunk_text,
                    "metadata": {
                        "token_count": current_chunk_tokens,
                        "sections": current_chunk_sections,
                        "chunk_type": "adaptive"
                    }
                })

                # Start a new chunk with this section
                current_chunk_text = f"# {section_heading}\n\n{section_text}" if section_heading else section_text
                current_chunk_tokens = section_tokens
                current_chunk_sections = [section_heading] if section_heading else []
            else:
                # Add section to current chunk
                if current_chunk_text:
                    section_with_heading = f"# {section_heading}\n\n{section_text}" if section_heading else section_text
                    current_chunk_text += "\n\n" + section_with_heading
                else:
                    current_chunk_text = f"# {section_heading}\n\n{section_text}" if section_heading else section_text

                current_chunk_tokens += section_tokens
                if section_heading:
                    current_chunk_sections.append(section_heading)

    # Add the last chunk if not empty
    if current_chunk_text:
        chunks.append({
            "text": current_chunk_text,
            "metadata": {
                "token_count": current_chunk_tokens,
                "sections": current_chunk_sections,
                "chunk_type": "adaptive"
            }
        })

    # Add overlap between chunks if specified
    if chunk_overlap > 0 and len(chunks) > 1:
        chunks_with_overlap = [chunks[0]]

        for i in range(1, len(chunks)):
            prev_chunk = chunks[i-1]["text"]
            current_chunk = chunks[i]["text"]

            # Create overlap by taking the end of the previous chunk
            overlap_text = create_overlap(prev_chunk, chunk_overlap, token_counter)

            # Add overlap to the beginning of the current chunk
            if overlap_text:
                new_text = overlap_text + "\n\n" + current_chunk
                new_tokens = token_counter(new_text)

                chunks[i]["text"] = new_text
                chunks[i]["metadata"]["token_count"] = new_tokens
                chunks[i]["metadata"]["has_overlap"] = True
                chunks[i]["metadata"]["overlap_tokens"] = token_counter(overlap_text)

            chunks_with_overlap.append(chunks[i])

        chunks = chunks_with_overlap

    # Add index information to chunks
    for i, chunk in enumerate(chunks):
        chunk["metadata"]["chunk_index"] = i
        chunk["metadata"]["total_chunks"] = len(chunks)

    logger.info(f"Created {len(chunks)} adaptive chunks")
    return chunks

# Helper function to create overlap from the end of a chunk
def create_overlap(text: str, overlap_tokens: int, token_counter: Callable[[str], int]) -> str:
    """Create an overlap text from the end of a chunk."""
    # For simplicity, we'll use a rough approximation for overlap
    # A more accurate implementation would use the actual tokenizer
    words = text.split()
    total_tokens = token_counter(text)

    if total_tokens <= overlap_tokens:
        return text

    # Estimate how many words to include for overlap
    approx_words_per_token = len(words) / total_tokens
    overlap_words = int(overlap_tokens * approx_words_per_token)

    # Take the last N words for overlap
    overlap_text = " ".join(words[-overlap_words:])
    return overlap_text

@flow(name="Smart Adaptive Chunking Flow")
def smart_adaptive_chunking_flow(
    file_path: str,
    base_chunk_size: int = 1000,
    min_chunk_size: int = 100,
    max_chunk_size: int = 2000,
    chunk_overlap: int = 200,
    structure_type: str = "markdown"
) -> List[Dict[str, Any]]:
    """Prefect flow for smart adaptive chunking of a document.

    Args:
        file_path: Path to the document file
        base_chunk_size: Base size of a chunk (in tokens)
        min_chunk_size: Minimum size of a chunk (in tokens)
        max_chunk_size: Maximum size of a chunk (in tokens)
        chunk_overlap: The number of tokens to overlap between chunks
        structure_type: Type of document structure (markdown, html, etc.)
    """
    # Load the document
    document = load_document(file_path)

    # Perform smart adaptive chunking
    chunks = smart_adaptive_chunking(
        text=document,
        base_chunk_size=base_chunk_size,
        min_chunk_size=min_chunk_size,
        max_chunk_size=max_chunk_size,
        chunk_overlap=chunk_overlap,
        structure_type=structure_type
    )

    return chunks

# Example usage
if __name__ == "__main__":
    # Example document path
    document_path = "path/to/your/document.md"

    # Run the flow
    chunks = smart_adaptive_chunking_flow(
        file_path=document_path,
        base_chunk_size=1000,
        min_chunk_size=100,
        max_chunk_size=2000,
        chunk_overlap=200,
        structure_type="markdown"  # Specify document structure type
    )

    # Print the first chunk as an example
    if chunks:
        print(f"First chunk: {chunks[0]['text'][:100]}...")
        print(f"Metadata: {chunks[0]['metadata']}")
        print(f"Total chunks created: {len(chunks)}")
This implementation provides a smart/adaptive chunking strategy with the following features:
- Adapts chunk boundaries based on document structure (headings, sections)
- Adjusts chunk size based on content complexity
- Respects minimum and maximum chunk size constraints
- Preserves document structure in the chunks
- Provides overlap between chunks for context continuity
- Includes detailed metadata about sections in each chunk
- Wrapped in a Prefect flow for easy integration into data pipelines

# EMBEDDING COMPONENT
@task(name="calculate_sentence_embeddings")
def calculate_embeddings(sentences, model_name):
    """
    Calculate embeddings for a list of sentences using a SentenceTransformer model.
    Args:
        sentences (list): List of strings to encode
        model_name (str): Name of the SentenceTransformer model to use
    Returns:
        tuple: (embeddings, similarities) - The embeddings array and similarity matrix
    """
    model = SentenceTransformer(model_name)
    embeddings = model.encode(sentences)
    print(f"Embeddings shape: {embeddings.shape}")
    similarities = model.similarity(embeddings, embeddings)
    print("Similarities matrix:")
    print(similarities)
    return embeddings, similarities
@flow(name="sentence_embedding_flow")
def sentence_embedding_flow():
    sentences = [
        "The weather is lovely today.",
        "It's so sunny outside!",
        "He drove to the stadium.",
    ]
    model_name = "nvidia/NV-Embed-v2"  # This parameter is allowed to be altered from the options mentioned
    embeddings, similarities = calculate_embeddings(sentences, model_name)
    return embeddings, similarities
if __name__ == "__main__":
    sentence_embedding_flow()
### Available parameters to alter
model_name:
- "intfloat/multilingual-e5-large-instruct"
- "nvidia/NV-Embed-v2"
- "Alibaba-NLP/gte-Qwen2-7B-instruct"
- "Alibaba-NLP/gte-Qwen2-1.5B-instruct"

# Cohere API
## embed-v4.0
- Embed Text
from prefect import task, flow
import cohere

# VECTOR_STORE COMPONENT

from prefect import flow, task
from pinecone import Pinecone
import os
import time

@task(name="initialize_pinecone")
def initialize_pinecone(api_key: str) -> Pinecone:
    """
    Initialize a Pinecone client with the provided API key.
    """
    pc = Pinecone(api_key=api_key)
    return pc

@task(name="create_index")
def create_index(
    pc: Pinecone,
    index_name: str,
    cloud: str = "aws",
    region: str = "us-east-1",
    model: str = "llama-text-embed-v2",
    field_map: dict = {"text": "chunk_text"}
) -> Pinecone.Index:
    """
    Create a dense index with integrated embedding model.
    """
    if not pc.has_index(index_name):
        pc.create_index_for_model(
            name=index_name,
            cloud=cloud,
            region=region,
            embed={"model": model, "field_map": field_map}
        )
        print(f"Index '{index_name}' created successfully")
    else:
        print(f"Index '{index_name}' already exists")
    return pc.Index(index_name)

@task(name="upsert_data")
def upsert_data(
    index: Pinecone.Index,
    namespace: str,
    records: list,
    wait_time: int = 10
) -> dict:
    """
    Upsert data records into the specified namespace.
    """
    index.upsert_records(namespace, records)
    print(f"Upserted {len(records)} records to namespace '{namespace}'")
    print(f"Waiting {wait_time} seconds for indexing to complete...")
    time.sleep(wait_time)
    stats = index.describe_index_stats()
    print("Index stats:")
    print(f"- Total vector count: {stats['total_vector_count']}")
    print(f"- Namespace '{namespace}' vector count: {stats['namespaces'].get(namespace, {{}}).get('vector_count', 0)}")
    return stats

@task(name="semantic_search")
def semantic_search(
    index: Pinecone.Index,
    namespace: str,
    query_text: str,
    top_k: int = 10,
    print_results: bool = True
) -> dict:
    """
    Perform semantic search on the index.
    """
    results = index.search(
        namespace=namespace,
        query={"top_k": top_k, "inputs": {'text': query_text}}
    )
    if print_results:
        print(f"\nSearch results for: '{query_text}'")
        for hit in results['result']['hits']:
            print(
                f"id: {hit['_id']:<5} | score: {round(hit['_score'], 2):<5} | "
                f"category: {hit['fields']['category']:<10} | "
                f"text: {hit['fields']['chunk_text']:<50}"
            )
    return results

@task(name="rerank_results")
def rerank_results(
    index: Pinecone.Index,
    namespace: str,
    query_text: str,
    top_k: int = 10,
    rerank_model: str = "bge-reranker-v2-m3",
    top_n: int = 10,
    rank_fields: list = ["chunk_text"],
    print_results: bool = True
) -> dict:
    """
    Perform semantic search with reranking on the index.
    """
    reranked = index.search(
        namespace=namespace,
        query={"top_k": top_k, "inputs": {'text': query_text}},
        rerank={"model": rerank_model, "top_n": top_n, "rank_fields": rank_fields}
    )
    if print_results:
        print(f"\nReranked results for: '{query_text}'")
        for hit in reranked['result']['hits']:
            print(
                f"id: {hit['_id']:<5} | score: {round(hit['_score'], 2):<5} | "
                f"category: {hit['fields']['category']:<10} | "
                f"text: {hit['fields']['chunk_text']:<50}"
            )
    return reranked

@task(name="delete_index")
def delete_index(pc: Pinecone, index_name: str) -> None:
    """
    Delete an index.
    """
    pc.delete_index(index_name)
    print(f"Index '{index_name}' deleted successfully")

@flow(name="add_documents_flow")
def add_documents_flow(
    api_key: str,
    index_name: str,
    namespace: str,
    documents: list,
    cloud: str = "aws",
    region: str = "us-east-1",
    model: str = "llama-text-embed-v2",
    field_map: dict = {"text": "chunk_text"},
    wait_time: int = 10
) -> dict:
    """
    Complete flow for adding documents to Pinecone.
    """
    pc = initialize_pinecone(api_key)
    index = create_index(pc, index_name, cloud, region, model, field_map)
    stats = upsert_data(index, namespace, documents, wait_time)
    return {"pc": pc, "index": index, "stats": stats}

@flow(name="search_documents_flow")
def search_documents_flow(
    api_key: str,
    index_name: str,
    namespace: str,
    query_text: str,
    top_k: int = 10,
    use_reranking: bool = False,
    rerank_model: str = "bge-reranker-v2-m3",
    top_n: int = 10,
    rank_fields: list = ["chunk_text"]
) -> dict:
    """
    Complete flow for searching documents in Pinecone.
    """
    pc = initialize_pinecone(api_key)
    index = pc.Index(index_name)
    if use_reranking:
        results = rerank_results(index, namespace, query_text, top_k, rerank_model, top_n, rank_fields)
    else:
        results = semantic_search(index, namespace, query_text, top_k)
    return {"pc": pc, "index": index, "results": results}

if __name__ == "__main__":
    # Replace with your actual API key
    API_KEY = "you-api-key" 
    
    # Define index and namespace names
    INDEX_NAME = "quickstart-demo"
    NAMESPACE = "example-namespace"
    
    # Example documents - in a real application, these would come from your data source
    sample_documents = [
        { "_id": "rec1", "chunk_text": "The Eiffel Tower was completed in 1889 and stands in Paris, France.", "category": "history" },
        { "_id": "rec2", "chunk_text": "Photosynthesis allows plants to convert sunlight into energy.", "category": "science" },
        { "_id": "rec3", "chunk_text": "Albert Einstein developed the theory of relativity.", "category": "science" },
        { "_id": "rec4", "chunk_text": "The mitochondrion is often called the powerhouse of the cell.", "category": "biology" },
        { "_id": "rec5", "chunk_text": "Shakespeare wrote many famous plays, including Hamlet and Macbeth.", "category": "literature" },
        { "_id": "rec6", "chunk_text": "Water boils at 100°C under standard atmospheric pressure.", "category": "physics" },
        { "_id": "rec7", "chunk_text": "The Great Wall of China was built to protect against invasions.", "category": "history" },
        { "_id": "rec8", "chunk_text": "Honey never spoils due to its low moisture content and acidity.", "category": "food science" },
        { "_id": "rec9", "chunk_text": "The speed of light in a vacuum is approximately 299,792 km/s.", "category": "physics" },
        { "_id": "rec10", "chunk_text": "Newton's laws describe the motion of objects.", "category": "physics" },
    ]
    
    # Option 1: Add documents to Pinecone
    add_result = add_documents_flow(
        api_key=API_KEY,
        index_name=INDEX_NAME,
        namespace=NAMESPACE,
        documents=sample_documents,
        wait_time=5  # Reduced wait time for testing
    )
    
    # Option 2: Search documents in Pinecone
    print("\nRunning search_documents_flow...")

    search_result = search_documents_flow(
        api_key=API_KEY,
        index_name=INDEX_NAME,
        namespace=NAMESPACE,
        query_text="Famous historical structures and monuments",
        top_k=5
    )
    
    # Option 3: Search with reranking
    print("\nRunning search_documents_flow with reranking...")
    reranked_search_result = search_documents_flow(
        api_key=API_KEY,
        index_name=INDEX_NAME,
        namespace=NAMESPACE,
        query_text="Famous historical structures and monuments",
        top_k=10,
        use_reranking=True,
        top_n=5
    )
    


import numpy as np
import uuid
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.models import Distance, VectorParams, PointStruct
from prefect import task, flow

# Optional imports for different embedding methods
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

@task(name="initialize_qdrant")
def initialize_qdrant(url=None, api_key=None, local=False):
    """
    Initialize a Qdrant client with various connection options.
    
    Args:
        url (str, optional): URL to Qdrant server or cloud
        api_key (str, optional): API key for Qdrant cloud
        local (bool): Whether to use local mode
        
    Returns:
        QdrantClient: The initialized Qdrant client
    """
    if local:
        return QdrantClient(path=":memory:")
    else:
        return QdrantClient(url=url, api_key=api_key)

@task(name="create_collection")
def create_collection(client, collection_name, vector_size=1536, distance="cosine"):
    """
    Create a new vector collection in Qdrant.
    
    Args:
        client (QdrantClient): The Qdrant client
        collection_name (str): Name for the new collection
        vector_size (int): Dimensionality of vectors
        distance (str): Distance metric ("cosine", "euclid", or "dot")
        
    Returns:
        bool: True if collection was created
    """
    
    # Check if collection already exists
    collections = client.get_collections().collections
    collection_names = [collection.name for collection in collections]
    
    if collection_name in collection_names:
        print(f"Collection '{collection_name}' already exists")
        return False
    
    # Create collection    
    client.create_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(
            size=vector_size, 
            distance=Distance.COSINE
        )
    )
    
    print(f"Collection '{collection_name}' created successfully")
    return True

@task(name="get_embeddings")
def get_embeddings(text, embedding_type="openai", model=None):
    """
    Get embeddings using various embedding models.
    
    Args:
        text (str or list): Text to embed (string or list of strings)
        embedding_type (str): Type of embedding to use
                             "openai" - OpenAI API (requires API key)
                             "sentence_transformers" - HuggingFace models
                             "fastembed" - Use Qdrant's FastEmbed (handled separately)
        model (str): Model name to use (depends on embedding_type)
                    For openai: "text-embedding-ada-002" etc.
                    For sentence_transformers: "all-MiniLM-L6-v2", "all-mpnet-base-v2", etc.
        
    Returns:
        list or ndarray: Embedding vector
    """
    if isinstance(text, list) and embedding_type != "fastembed":
        return [get_embeddings(t, embedding_type, model) for t in text]
    
    if embedding_type == "openai":
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI package not installed. Install with 'pip install openai'")
        
        model = model or "text-embedding-ada-002"
        response = openai.Embedding.create(
            input=text,
            model=model
        )
        return response['data'][0]['embedding']
    
    elif embedding_type == "sentence_transformers":
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("Sentence Transformers not installed. Install with 'pip install sentence-transformers'")
        
        model_name = model or "all-MiniLM-L6-v2"  # Default to a good general purpose model
        model = SentenceTransformer(model_name)
        
        # Get embeddings
        embedding = model.encode(text)
        return embedding.tolist() if isinstance(embedding, np.ndarray) else embedding
    
    else:
        raise ValueError(f"Unsupported embedding type: {embedding_type}")

@task(name="setup_fastembed")
def setup_fastembed(client, model_name="BAAI/bge-base-en", gpu=False):
    """
    Set up FastEmbed for the client. This is Qdrant's built-in embedding feature.
    
    Args:
        client (QdrantClient): The Qdrant client
        model_name (str): Name of the embedding model to use
        gpu (bool): Whether to use GPU for embeddings
        
    Returns:
        QdrantClient: The client with FastEmbed configured
    """
    if gpu:
        client.set_model(
            model_name,
            providers=["CUDAExecutionProvider", "CPUExecutionProvider"]
        )
    else:
        client.set_model(model_name)
    
    print(f"FastEmbed initialized with model: {model_name}")
    return client

@task(name="upsert_chunks")
def upsert_chunks(client, collection_name, chunks, embedding_type="openai", embedding_model=None):
    """
    Embed chunks and upsert into Qdrant collection.
    
    Args:
        client (QdrantClient): The Qdrant client
        collection_name (str): Name of the collection
        chunks (list): List of text chunks to embed and store
        embedding_type (str): Type of embedding to use ("openai", "sentence_transformers", or "fastembed")
        embedding_model (str): Model name to use
        
    Returns:
        dict: Operation result
    """
    points = []
    
    if embedding_type == "fastembed":
        # For FastEmbed, we'll let Qdrant handle the embedding
        # First, ensure FastEmbed is set up
        setup_fastembed(client, model_name=embedding_model or "BAAI/bge-base-en")
        
        # For FastEmbed, we'll use the client.add method
        result = client.add(
            collection_name=collection_name,
            documents=chunks,
            metadata=[{"index": i} for i in range(len(chunks))],
            ids=[str(uuid.uuid4()) for _ in range(len(chunks))]
        )
        
        print(f"Added {len(chunks)} chunks to the collection using FastEmbed")
        return result
    
    else:
        # For other embedding types, we'll handle the embedding ourselves
        for chunk in chunks:
            # Generate embedding
            embedding = get_embeddings(chunk, embedding_type=embedding_type, model=embedding_model)
            
            # Create a unique ID
            point_id = str(uuid.uuid4())
            
            # Create point
            points.append(
                PointStruct(
                    id=point_id,
                    vector=embedding,
                    payload={"text": chunk}
                )
            )
        
        # Upsert points
        result = client.upsert(
            collection_name=collection_name,
            points=points,
            wait=True
        )
        
        print(f"Added {len(chunks)} chunks to the collection using {embedding_type}")
        return result

@task(name="semantic_search")
def semantic_search(client, collection_name, query, embedding_type="openai", embedding_model=None, limit=3):
    """
    Perform semantic search on the collection.
    
    Args:
        client (QdrantClient): The Qdrant client
        collection_name (str): Name of the collection
        query (str): Search query
        embedding_type (str): Type of embedding to use ("openai", "sentence_transformers", or "fastembed")
        embedding_model (str): Model name to use
        limit (int): Number of results to return
        
    Returns:
        list: Search results
    """
    if embedding_type == "fastembed":
        # For FastEmbed, we use the client.query method
        search_results = client.search(
            collection_name=collection_name,
            query_text=query,
            limit=limit
        )
    else:
        # Get query embedding
        query_embedding = get_embeddings(query, embedding_type=embedding_type, model=embedding_model)
        
        # Search the collection
        search_results = client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=limit
        )
    
    return search_results

@flow(name="main")
def main():
    # Cloud Qdrant configuration
    QDRANT_URL = "https://aa97a128-a25f-4bf2-ac0a-e9863ebe178b.us-west-2-0.aws.cloud.qdrant.io:6333"  # Replace with your Qdrant cloud URL
    QDRANT_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.IAgacZmccrWyeZOdeUVI_JGkZ-5IVi8DE6H3PKDvNd0"  # Replace with your Qdrant API key
    
    # Choose your embedding method
    EMBEDDING_TYPE = "sentence_transformers"  # Options: "openai", "sentence_transformers", "fastembed"
    EMBEDDING_MODEL = "all-MiniLM-L6-v2"      # Model name depends on the embedding type
    
    # if EMBEDDING_TYPE == "sentence_transformers":
    #     EMBEDDING_MODEL = "BAAI/bge-base-en"  # Sentence Transformers model

    # OpenAI-specific configuration (only needed if using OpenAI embeddings)
    if EMBEDDING_TYPE == "openai":
        openai.api_key = "your-openai-api-key"  # Replace with your actual API key
        EMBEDDING_MODEL = "text-embedding-ada-002"  # OpenAI model
    
    if EMBEDDING_TYPE == "fastembed":
        EMBEDDING_MODEL = "thenlper/gte-large"  # FastEmbed model

    # Initialize Qdrant client (cloud mode)
    client = initialize_qdrant(url=QDRANT_URL, api_key=QDRANT_API_KEY, local=False)
    print(f"Initialized Qdrant client connected to: {QDRANT_URL}")
    
    # Collection name
    collection_name = "sample-collection8"
    
    # Vector size depends on the embedding model
    vector_size_map = {
        "openai": {
            "text-embedding-ada-002": 1536
        },
        "sentence_transformers": {
            "all-MiniLM-L6-v2": 384,
            "all-mpnet-base-v2": 768,
            "all-distilroberta-v1": 768,
            "paraphrase-multilingual-MiniLM-L12-v2": 768
        },
        "fastembed": {
            "BAAI/bge-base-en": 768,
            "BAAI/bge-small-en": 768,
            "thenlper/gte-large": 768,
        }
    }
    
    vector_size = vector_size_map[EMBEDDING_TYPE].get(EMBEDDING_MODEL, 768)
    
    create_collection(
        client,
        collection_name,
        vector_size=vector_size,
    )
    
    # Sample chunks - replace with your own sample text
    sample_chunks = [
        "The Eiffel Tower was completed in 1889 and stands in Paris, France.",
        "Photosynthesis allows plants to convert sunlight into energy.",
        "Albert Einstein developed the theory of relativity.",
        "The mitochondrion is often called the powerhouse of the cell.",
        "Shakespeare wrote many famous plays, including Hamlet and Macbeth.",
        "Water boils at 100°C under standard atmospheric pressure.",
        "The Great Wall of China was built to protect against invasions.",
        "Honey never spoils due to its low moisture content and acidity.",
        "The speed of light in a vacuum is approximately 299,792 km/s.",
        "Newton's laws describe the motion of objects."
    ]
    
    # Upload chunks to Qdrant
    upsert_chunks(
        client, 
        collection_name, 
        sample_chunks,
        embedding_type=EMBEDDING_TYPE,
        embedding_model=EMBEDDING_MODEL
    )
    
    # Example search query
    query = "Famous historical structures and monuments"
    print(f"\nSearching for: '{query}'")
    
    # Perform search
    results = semantic_search(
        client, 
        collection_name, 
        query,
        embedding_type=EMBEDDING_TYPE,
        embedding_model=EMBEDDING_MODEL
    )
    
    # Display results
    print("\nSearch results:")
    for i, result in enumerate(results):
        print(f"{i+1}. Score: {result.score:.4f}")
        print(f"   Text: {result.payload['text']}")
        print()

if __name__ == "__main__":
    main()


# LLM COMPONENT
@task(name="generate_azure_completions")
def generate_completions(
    messages: List[Dict[str, str]], 
    model_name: str,
    temperature: Optional[float] = None,
    top_p: Optional[float] = None,
    n: Optional[int] = None,
    stream: Optional[bool] = None,
    max_tokens: Optional[int] = None,
    echo: Optional[bool] = None,
    presence_penalty: Optional[float] = None,
    frequency_penalty: Optional[float] = None,
    logit_bias: Optional[Dict[str, float]] = None,
    logprobs: Optional[int] = None,
    user: Optional[str] = None,
    stop: Optional[Union[str, List[str]]] = None,
    seed: Optional[int] = None,
    suffix: Optional[str] = None,
    azure_endpoint: Optional[str] = None,
    api_key: Optional[str] = None,
    api_version: Optional[str] = None
):
    """
    Generate completions using Azure OpenAI API.
    Args:
        messages: List of message dictionaries with 'role' and 'content' (REQUIRED)
        model_name: Name of the deployment model to use (REQUIRED)
        temperature: Controls randomness (0-2)
        top_p: Controls diversity via nucleus sampling (0-1)
        n: Number of completions to generate
        stream: Whether to stream responses
        max_tokens: Maximum number of tokens to generate
        presence_penalty: Penalty for new tokens based on presence in text (0-2)
        frequency_penalty: Penalty for new tokens based on frequency in text (0-2)
        logit_bias: Bias for specific tokens
        logprobs: Number of logprobs to return
        user: Unique user identifier
        stop: Sequences where API will stop generating
        seed: Random number generator seed
        azure_endpoint: Azure OpenAI endpoint URL
        api_key: Azure OpenAI API key
        api_version: API version to use
    Returns:
        The completion text
    """
    client = AzureOpenAI(
        azure_endpoint=azure_endpoint,
        api_key=api_key,
        api_version=api_version
    )
    optional_params = {
        "temperature": temperature,
        "top_p": top_p,
        "n": n,
        "stream": stream,
        "max_tokens": max_tokens,
        "echo": echo,
        "presence_penalty": presence_penalty,
        "frequency_penalty": frequency_penalty,
        "logit_bias": logit_bias,
        "logprobs": logprobs,
        "user": user,
        "stop": stop,
        "seed": seed,
        "suffix": suffix,
    }
    completion_params = {
        "model": model_name,
        "messages": messages,
    }
    completion_params.update({k: v for k, v in optional_params.items() if v is not None})
    response = client.chat.completions.create(**completion_params)
    completion = response.choices[0].message.content
    return completion
@flow(name="azure_openai_flow")
def azure_openai_flow(
    system_message: str,
    user_message: str,
    model_deployment: Optional[str] = None,
    azure_endpoint: Optional[str] = None,
    api_key: Optional[str] = None,
    api_version: Optional[str] = None
    **kwargs
):
    messages = [
        {"role": "system", "content": system_message},
        {"role": "user", "content": user_message}
    ]
    if system_message is None:
        raise ValueError("System message is required")
    if user_message is None:
        raise ValueError("User message is required")
    completion = generate_completions(messages=messages, model_name=model_deployment,azure_endpoint=azure_endpoint,api_key=api_key,api_version=api_version)
    return completion
if __name__ == "__main__":
    azure_openai_flow()
- `model_id`: The Bedrock model ID to use (e.g., 'anthropic.claude-3-sonnet-20240229-v1:0', etc.)
- `region_name`: AWS region name (e.g., 'us-east-1')
- `aws_access_key_id`: AWS access key ID (string)
- `aws_secret_access_key`: AWS secret access key (string)
- `temperature`: Controls randomness (0-1)
- `top_p`: Controls diversity via nucleus sampling (0-1)
- `top_k`: Number of highest probability tokens to consider (int)
- `max_tokens`: Maximum number of tokens to generate (int)
- `stop_sequences`: List of sequences where the model will stop generating (list of strings)
- `anthropic_version`: Version of the Anthropic API to use (string)
- `system_message`: The system message for Claude (string, optional)
- `user_message`: The user message to complete (string)
import os
import json
import boto3
from prefect import task, flow
from typing import List, Dict, Optional, Union, Any



# TOOL_TAVILY COMPONENT
- `query`: The search query string (max 400 characters)
- `search_depth`: 'basic' or 'advanced'
- `include_domains`: List of domains to include in search results
- `exclude_domains`: List of domains to exclude from search results
- `max_results`: Maximum number of search results to return
- `chunks_per_source`: Number of content chunks to return per source (advanced search)
- `include_raw_content`: Whether to include raw content in results
- `time_range`: Time range for results (e.g., 'day', 'week', 'month')
- `topic`: Filter for specific topics (e.g., 'news')
- `days`: Number of days back for news results
- `min_score`: Minimum relevance score for filtering results
- `urls`: List of URLs to extract content from
- `extract_depth`: 'basic' or 'advanced'
- `start_url`: The URL to start crawling from
- `instructions`: Optional instructions for the crawl
- `max_pages`: Maximum number of pages to crawl
- `max_depth`: Maximum depth level for crawling
- `max_breadth`: Maximum number of links to follow at each level
- `extract_depth`: 'basic' or 'advanced'
- `select_paths`: List of regex patterns for paths to include
- `exclude_paths`: List of regex patterns for paths to exclude
- `select_domains`: List of regex patterns for domains to include
- `exclude_domains`: List of regex patterns for domains to exclude
- `categories`: List of content categories to focus on
- `include_images`: Whether to include images in the extraction
- All parameters from `tavily_search`, `tavily_extract`, and `tavily_crawl` can be altered via the flow's arguments.
from prefect import task, flow
import os
import re
from typing import Dict, Any, Optional, List, Union
from tavily import TavilyClient
import asyncio

@task(name="tavily_search_task")
def tavily_search(
    query: str, 
    api_key: Optional[str] = None,
    search_depth: str = "basic",
    include_domains: Optional[List[str]] = None,
    exclude_domains: Optional[List[str]] = None,
    max_results: int = 10,
    chunks_per_source: Optional[int] = None,
    include_raw_content: bool = False,
    time_range: Optional[str] = None,
    topic: Optional[str] = None,
    days: Optional[int] = None,
    min_score: Optional[float] = None
) -> Dict[str, Any]:
    """
    Search the web using Tavily's API.
    
    Args:
        query: The search query string (max 400 characters)
        api_key: Tavily API key (defaults to TAVILY_API_KEY environment variable)
        search_depth: 'basic' or 'advanced' search depth
        include_domains: List of domains to include in search results
        exclude_domains: List of domains to exclude from search results
        max_results: Maximum number of search results to return
        chunks_per_source: Number of content chunks to return per source (used with advanced search)
        include_raw_content: Whether to include raw content in results
        time_range: Time range for results (e.g., 'day', 'week', 'month')
        topic: Filter for specific topics (e.g., 'news')
        days: Number of days back for news results
        min_score: Minimum relevance score for filtering results
        
    Returns:
        Dict containing search results
    """
    try:
        # Validate query length
        if len(query) > 400:
            raise ValueError("Query is too long. Max query length is 400 characters.")
            
        # Validate search_depth
        if search_depth not in ["basic", "advanced"]:
            raise ValueError("search_depth must be either 'basic' or 'advanced'")
        
        # Get API key from parameters or environment
        api_key = api_key or os.environ.get("TAVILY_API_KEY")
        if not api_key:
            raise ValueError("Tavily API key is required. Provide it as a parameter or set TAVILY_API_KEY environment variable.")
            
        # Initialize client
        client = TavilyClient(api_key=api_key)
        
        params = {
            "query": query,
            "search_depth": search_depth,
            "max_results": max_results
        }
        optional_params = {
            "include_domains": include_domains,
            "exclude_domains": exclude_domains,
            "include_raw_content": include_raw_content,
            "time_range": time_range,
            "topic": topic,
            "chunks_per_source": chunks_per_source if search_depth == "advanced" else None,
            "days": days if topic == "news" else None
        }
        params.update({k: v for k, v in optional_params.items() if v is not None})
        
            
        # Execute search with parameters
        response = client.search(**params)
        
        # Apply post-processing if needed
        if min_score is not None and "results" in response:
            response["results"] = [r for r in response["results"] if r.get("score", 0) >= min_score]
            
        return response
        
    except Exception as e:
        print(f"Error searching with Tavily: {str(e)}")
        raise

# Sample Output for tavily_search
# {
#   'query': 'latest advances in artificial intelligence',
#   'follow_up_questions': None,
#   'answer': None,
#   'images': [],
#   'results': [
#     {
#       'title': "Year in review: Google's biggest AI advancements of 2024",
#       'url': 'https://blog.google/technology/ai/2024-ai-extraordinary-progress-advancement/',
#       'content': '...',
#       'score': 0.71948266,
#       'raw_content': None
#     },
#     ...
#   ],
#   'response_time': 1.67
# }

@task(name="tavily_extract_task")
def tavily_extract(urls: List[str], api_key: Optional[str] = None, extract_depth: str = "advanced") -> Dict[str, Any]:
    """
    Extract content from a URL using Tavily's API.
    
    Args:
        url: The URL to extract content from
        api_key: Tavily API key (defaults to TAVILY_API_KEY environment variable)
        extract_depth: Level of extraction depth ("basic" or "advanced")
        
    Returns:
        Dict containing extracted content
    """
    try:
        # Get API key from parameters or environment
        api_key = api_key or os.environ.get("TAVILY_API_KEY")
        if not api_key:
            raise ValueError("Tavily API key is required. Provide it as a parameter or set TAVILY_API_KEY environment variable.")
            
        # Initialize client
        client = TavilyClient(api_key=api_key)
        
        # Execute extraction with advanced depth by default
        response = client.extract(urls=urls, extract_depth=extract_depth)
        
        return response
    except Exception as e:
        print(f"Error extracting content with Tavily: {str(e)}")
        raise

# Sample Output for tavily_extract
# {
#   'results': [
#     {
#       'url': 'https://www.anthropic.com/',
#       'raw_content': '...',
#       'images': []
#     }
#   ],
#   'failed_results': [],
#   'response_time': 0.01
# }

@task(name="tavily_crawl_task")
def tavily_crawl(
    start_url: str,
    api_key: Optional[str] = None,
    instructions: Optional[str] = None,
    max_pages: int = 10,
    max_depth: int = 1,
    max_breadth: Optional[int] = None,
    extract_depth: str = "basic",
    select_paths: Optional[List[str]] = None,
    exclude_paths: Optional[List[str]] = None,
    select_domains: Optional[List[str]] = None,
    exclude_domains: Optional[List[str]] = None,
    categories: Optional[List[str]] = None,
    include_images: bool = False
) -> Dict[str, Any]:
    """
    Crawl a website starting from a URL using Tavily's API.
    Note: This feature is in invite-only beta.
    
    Args:
        start_url: The URL to start crawling from
        api_key: Tavily API key (defaults to TAVILY_API_KEY environment variable)
        instructions: Optional instructions for the crawl (e.g., "Find all pages on the Python SDK")
        max_pages: Maximum number of pages to crawl (limit)
        max_depth: Maximum depth level for crawling (default: 1, increase carefully)
        max_breadth: Maximum number of links to follow at each level
        extract_depth: Level of content extraction ("basic" or "advanced")
        select_paths: List of regex patterns for paths to include
        exclude_paths: List of regex patterns for paths to exclude
        select_domains: List of regex patterns for domains to include
        exclude_domains: List of regex patterns for domains to exclude
        categories: List of content categories to focus on
        include_images: Whether to include images in the extraction
        
    Returns:
        Dict containing crawl results
    """
    try:
        # Get API key from parameters or environment
        api_key = api_key or os.environ.get("TAVILY_API_KEY")
        if not api_key:
            raise ValueError("Tavily API key is required. Provide it as a parameter or set TAVILY_API_KEY environment variable.")
            
        # Initialize client
        client = TavilyClient(api_key=api_key)
        
        # Build crawl parameters
        crawl_params = {
            "url": start_url,
            "limit": max_pages,
            "max_depth": max_depth,
            "extract_depth": extract_depth
        }
        
        # Add optional parameters if provided
        optional_params = {
            "instructions": instructions,
            "max_breadth": max_breadth,
            "select_paths": select_paths,
            "exclude_paths": exclude_paths, 
            "select_domains": select_domains,
            "exclude_domains": exclude_domains,
            "categories": categories,
            "include_images": include_images
        }

        # Filter out None values and update crawl_params
        crawl_params.update({k: v for k, v in optional_params.items() if v is not None})
        
        response = client.crawl(**crawl_params)
        return response
    except Exception as e:
        print(f"Error crawling with Tavily: {str(e)}")
        raise

# Sample Output for tavily_crawl
# {
#   'base_url': 'https://www.anthropic.com/',
#   'results': [
#     {
#       'url': 'https://www.anthropic.com/claude',
#       'raw_content': '...',
#       'images': []
#     },
#     ...
#   ],
#   'response_time': 7.1
# }

@flow(name="tavily_api_examples_flow")
def tavily_api_examples(
    api_key: str = None,
    # Search params
    search_query: str = "latest advances in artificial intelligence",
    search_depth: str = "basic",
    include_domains: Optional[List[str]] = None,
    exclude_domains: Optional[List[str]] = None,
    max_results: int = 5,
    chunks_per_source: Optional[int] = None,
    include_raw_content: bool = False,
    time_range: Optional[str] = None,
    topic: Optional[str] = None,
    days: Optional[int] = None,
    min_score: Optional[float] = None,
    # Extract params
    extract_urls: Union[str, List[str]] = "https://www.anthropic.com/",
    extract_depth: str = "basic",
    # Crawl params
    crawl_start_url: str = "https://www.anthropic.com/",
    crawl_instructions: Optional[str] = "Find information about Claude AI",
    crawl_max_pages: int = 3,
    crawl_max_depth: int = 1,
    crawl_max_breadth: Optional[int] = None,
    crawl_extract_depth: str = "basic",
    crawl_select_paths: Optional[List[str]] = None,
    crawl_exclude_paths: Optional[List[str]] = None,
    crawl_select_domains: Optional[List[str]] = None,
    crawl_exclude_domains: Optional[List[str]] = None,
    crawl_categories: Optional[List[str]] = None,
    crawl_include_images: bool = False
) -> Dict[str, Any]:
    api_key = api_key or os.environ.get("TAVILY_API_KEY")
    results = {}

    # Example 1: Basic web search

    search_results = tavily_search(
        query=search_query,
        api_key=api_key,
        search_depth=search_depth,
        include_domains=include_domains,
        exclude_domains=exclude_domains,
        max_results=max_results,
        chunks_per_source=chunks_per_source,
        include_raw_content=include_raw_content,
        time_range=time_range,
        topic=topic,
        days=days,
        min_score=min_score
    )
    results["search"] = search_results

    print("Search Results:", search_results)

    # Example 2: Extract content from a URL

    extract_results = tavily_extract(
        urls=extract_urls,
        api_key=api_key,
        extract_depth=extract_depth
    )
    results["extract"] = extract_results
  
    print(f"Extracted content from {extract_urls} : {extract_results}")

    # Example 3: Crawl a website

    crawl_results = tavily_crawl(
        start_url=crawl_start_url,
        api_key=api_key,
        instructions=crawl_instructions,
        max_pages=crawl_max_pages,
        max_depth=crawl_max_depth,
        max_breadth=crawl_max_breadth,
        extract_depth=crawl_extract_depth,
        select_paths=crawl_select_paths,
        exclude_paths=crawl_exclude_paths,
        select_domains=crawl_select_domains,
        exclude_domains=crawl_exclude_domains,
        categories=crawl_categories,
        include_images=crawl_include_images
    )
    results["crawl"] = crawl_results

    print(f"Crawled results: {crawl_results}")

    return results

if __name__ == "__main__":
    # Call the example function
    results = tavily_api_examples()
       
- `serper_api_key`: API key for the Serper Google Search API (optional if set as environment variable)
- `aiosession`: Optional aiohttp ClientSession for async requests
- `gl`: Country code for the search (e.g., 'us' for United States)
- `hl`: Language code for the search (e.g., 'en' for English)
- `k`: Number of search results to return
- `result_key_for_type`: Mapping of search type to result key
- `serper_api_url`: Custom API URL for Serper requests


from langchain_community.utilities import GoogleSerperAPIWrapper
from langchain_groq import ChatGroq
from langchain.agents import initialize_agent, Tool
from langchain.agents import AgentType
import os
from typing import Optional, Dict, Any
from aiohttp import ClientSession
import asyncio

def initialize_serper_tool(
    serper_api_key: Optional[str] = None,
    aiosession: Optional[ClientSession] = None,
    gl: Optional[str] = None,
    hl: Optional[str] = None,
    k: Optional[int] = None,
    result_key_for_type: Optional[Dict[str, str]] = None,
    serper_api_url: Optional[str] = None
) -> Tool:
    """
    Initialize the Google Serper search tool with parameters from the documentation.
    
    Args:
        serper_api_key: API key for the Serper Google Search API (optional if set as env var)
        aiosession: Optional aiohttp ClientSession
        gl: Optional country code for the search
        hl: Optional language code for the search
        k: Optional number of search results to return
        result_key_for_type: Optional mapping of search type to result key
        serper_api_url: Optional custom API URL
    
    Returns:
        Tool object configured for Google Serper search
    """
    # Set the API key in the environment if provided
    if serper_api_key:
        os.environ["SERPER_API_KEY"] = serper_api_key
    
    # Prepare kwargs with only non-None values
    kwargs = {k: v for k, v in {'serper_api_key': serper_api_key, 'aiosession': aiosession, 'gl': gl, 'hl': hl, 'k': k, 'result_key_for_type': result_key_for_type, 'serper_api_url': serper_api_url}.items() if v is not None}
    
    # Initialize the Google Serper wrapper with only provided parameters
    search = GoogleSerperAPIWrapper(**kwargs)
    
    # Create a tool from the wrapper
    serper_tool = Tool(
        name="Search",
        func=search.run,
        description="Useful for when you need to search for information on the web. Input should be a search query."
    )
    
    print("Google Serper Search tool initialized successfully.")
    
    return serper_tool

def demonstrate_serper_capabilities(
    user_question: str,
    llm_api_key: str,
    serper_api_key: Optional[str] = None,
    llm_model_name: str = "llama-3.3-70b-versatile",
    verbose: bool = True,
    gl: Optional[str] = None,
    hl: Optional[str] = None,
    k: Optional[int] = None,
    result_key_for_type: Optional[Dict[str, str]] = None,
    serper_api_url: Optional[str] = None
) -> str:
    """
    Demonstrates the capabilities of the Google Serper API integration with LangChain.
    
    Args:
        user_question: The question to ask that requires web search
        llm_api_key: API key for Groq
        serper_api_key: API key for the Serper Google Search API (optional if set as env var)
        llm_model_name: Model name for Groq (default: "llama-3.3-70b-versatile")
        verbose: Whether to print detailed agent reasoning
        gl: Optional country code for the search
        hl: Optional language code for the search
        k: Optional number of search results to return
        result_key_for_type: Optional mapping of search type to result key
        serper_api_url: Optional custom API URL
    
    Returns:
        The final answer from the agent
    """
    
    # Initialize the serper tool with documented parameters
    serper_tool = initialize_serper_tool(
        serper_api_key=serper_api_key,
        gl=gl,
        hl=hl,
        k=k,
        result_key_for_type=result_key_for_type,
        serper_api_url=serper_api_url
    )
    
    # Initialize the language model
    llm = ChatGroq(temperature=0, 
                   groq_api_key=llm_api_key, 
                   model_name=llm_model_name)
    
    # Create an agent with the tool - using ZERO_SHOT_REACT_DESCRIPTION
    agent = initialize_agent(
        [serper_tool], 
        llm, 
        agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
        verbose=verbose,
        handle_parsing_errors=True
    )
    
    # Run the agent with the user's question
    try:
        result = agent.run(user_question)
        print(f"Final answer: {result}")
        return result
    except Exception as e:
        print(f"An error occurred: {e}")
        return str(e)

# Asynchronous version

async def initialize_serper_tool_async(
    serper_api_key: Optional[str] = None,
    aiosession: Optional[ClientSession] = None,
    gl: Optional[str] = None,
    hl: Optional[str] = None,
    k: Optional[int] = None,
    result_key_for_type: Optional[Dict[str, str]] = None,
    serper_api_url: Optional[str] = None
) -> Tool:
    """
    Initialize the Google Serper search tool asynchronously with documented parameters.
    
    Args:
        serper_api_key: API key for the Serper Google Search API (optional if set as env var)
        aiosession: Optional aiohttp ClientSession
        gl: Optional country code for the search
        hl: Optional language code for the search
        k: Optional number of search results to return
        result_key_for_type: Optional mapping of search type to result key
        serper_api_url: Optional custom API URL
    
    Returns:
        Tool object configured for Google Serper search
    """
    # Set the API key in the environment if provided
    if serper_api_key:
        os.environ["SERPER_API_KEY"] = serper_api_key
    
    kwargs = {k: v for k, v in {'serper_api_key': serper_api_key, 'aiosession': aiosession, 'gl': gl, 'hl': hl, 'k': k, 'result_key_for_type': result_key_for_type, 'serper_api_url': serper_api_url}.items() if v is not None}
    
    # Initialize the Google Serper wrapper with only provided parameters
    search = GoogleSerperAPIWrapper(**kwargs)
    
    # Create a tool from the wrapper using the async version
    serper_tool = Tool(
        name="Search",
        func=search.arun,  # Use the async run method
        description="Useful for when you need to search for information on the web. Input should be a search query.",
        coroutine=search.arun  # Specify the coroutine
    )
    
    print("Google Serper Search tool initialized successfully (async).")
    
    return serper_tool

async def demonstrate_serper_capabilities_async(
    user_question: str,
    llm_api_key: str,
    serper_api_key: Optional[str] = None,
    llm_model_name: str = "llama-3.3-70b-versatile",
    verbose: bool = True,
    gl: Optional[str] = None,
    hl: Optional[str] = None,
    k: Optional[int] = None,
    result_key_for_type: Optional[Dict[str, str]] = None,
    serper_api_url: Optional[str] = None
) -> str:
    """
    Demonstrates the capabilities of the Google Serper API integration with LangChain asynchronously.
    
    Args:
        user_question: The question to ask that requires web search
        llm_api_key: API key for Groq
        serper_api_key: API key for the Serper Google Search API (optional if set as env var)
        llm_model_name: Model name for Groq (default: "llama-3.3-70b-versatile")
        verbose: Whether to print detailed agent reasoning
        gl: Optional country code for the search
        hl: Optional language code for the search
        k: Optional number of search results to return
        result_key_for_type: Optional mapping of search type to result key
        serper_api_url: Optional custom API URL
    
    Returns:
        The final answer from the agent
    """
    # Create an aiohttp session for reuse
    async with ClientSession() as session:
        # Initialize the serper tool with documented parameters
        serper_tool = await initialize_serper_tool_async(
            serper_api_key=serper_api_key,
            aiosession=session,
            gl=gl,
            hl=hl,
            k=k,
            result_key_for_type=result_key_for_type,
            serper_api_url=serper_api_url
        )
        
        # Initialize the language model
        llm = ChatGroq(temperature=0, 
                      groq_api_key=llm_api_key, 
                      model_name=llm_model_name)
        
        # Create an agent with the tool
        agent = initialize_agent(
            [serper_tool], 
            llm, 
            agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
            verbose=verbose,
            handle_parsing_errors=True
        )
        
        # Run the agent with the user's question
        try:
            # Use agent.arun for async execution
            result = await agent.arun(user_question)
            print(f"Final answer: {result}")
            return result
        except Exception as e:
            print(f"An error occurred: {e}")
            return str(e)

# Example usage
async def main():
    serper_api_key = "3b9bd5471301b85b5f42ac7c3a7b8e49e62a36b4"
    llm_api_key = "********************************************************"
    user_question = "Who is the current president of the United States?"
    
    # Run the async version
    result = await demonstrate_serper_capabilities_async(
        user_question=user_question,
        llm_api_key=llm_api_key,
        serper_api_key=serper_api_key
    )
    print(result)

# Run the async main function
if __name__ == "__main__":
    # For synchronous usage
    # result = demonstrate_serper_capabilities(
    #     user_question="What is the capital of France?",
    #     llm_api_key="YOUR_GROQ_API_KEY",
    #     serper_api_key="YOUR_SERPER_API_KEY"
    # )
    # print(result)
    
    # For asynchronous usage
    asyncio.run(main())

- `q`: The user's search query (max 400 chars, 50 words)
- `country`: 2-letter country code (default 'US')
- `search_lang`: Language for results (default 'en')
- `ui_lang`: UI language (e.g., 'en-US')
- `count`: # of results to return (max 20)
- `offset`: Page offset (zero-based)
- `safesearch`: 'off'|'moderate'|'strict'
- `freshness`: 'pd'|'pw'|'pm'|'py' or 'YYYY-MM-DDtoYYYY-MM-DD'
- `text_decorations`: Highlight markup in snippets
- `spellcheck`: Auto-spellcheck query
- `result_filter`: CSV of result types (e.g., 'news,videos')
- `goggles_id`: Deprecated single-ID goggles 
- `goggles`: List of goggles (URLs or definitions)
- `units`: 'metric' or 'imperial'
- `extra_snippets`: Return up to 5 extra excerpts
- `summary`: Enable summary key generation
- `api_key`: Brave API key (or set BRAVE_API_KEY env var)
import os
import asyncio
from typing import Any, Dict, List, Optional
from brave import Brave, AsyncBrave
from prefect import task, flow


@task(name="Brave Search Sync Task")
def brave_search(
    q: str,
    *,
    country: str = "US",
    search_lang: str = "en",
    ui_lang: str = "en-US",
    count: int = 20,
    offset: int = 0,
    safesearch: str = "moderate",
    freshness: Optional[str] = None,
    text_decorations: bool = True,
    spellcheck: bool = True,
    result_filter: Optional[str] = None,
    goggles_id: Optional[str] = None,
    goggles: Optional[List[str]] = None,
    units: Optional[str] = None,
    extra_snippets: bool = False,
    summary: bool = False,
    api_key: Optional[str] = None
) -> Dict[str, Any]:
    if not q or len(q) > 400:
        raise ValueError("Query must be non-empty and at most 400 characters.")
    if len(q.split()) > 50:
        raise ValueError("Query must be at most 50 words.")

    api_key = api_key or os.environ.get("BRAVE_API_KEY")
    if not api_key:
        raise ValueError("Brave API key is required. Provide it or set BRAVE_API_KEY.")

    client = Brave(api_key=api_key)

    params: Dict[str, Any] = {
        "q": q,
        "country": country,
        "search_lang": search_lang,
        "ui_lang": ui_lang,
        "count": count,
        "offset": offset,
        "safesearch": safesearch,
        "freshness": freshness,
        "text_decorations": int(text_decorations),
        "spellcheck": int(spellcheck),
        "result_filter": result_filter,
        "goggles_id": goggles_id,
        "goggles": goggles,
        "units": units,
        "extra_snippets": int(extra_snippets),
        "summary": int(summary),
    }
    params = {k: v for k, v in params.items() if v is not None}

    return client.search(**params)


@task(name="Brave Search Async Task")
def brave_search_async(
    q: str,
    *,
    country: str = "US",
    search_lang: str = "en",
    ui_lang: str = "en-US",
    count: int = 20,
    offset: int = 0,
    safesearch: str = "moderate",
    freshness: Optional[str] = None,
    text_decorations: bool = True,
    spellcheck: bool = True,
    result_filter: Optional[str] = None,
    goggles_id: Optional[str] = None,
    goggles: Optional[List[str]] = None,
    units: Optional[str] = None,
    extra_snippets: bool = False,
    summary: bool = False,
    api_key: Optional[str] = None
) -> Dict[str, Any]:
    if not q or len(q) > 400:
        raise ValueError("Query must be non-empty and at most 400 characters.")
    if len(q.split()) > 50:
        raise ValueError("Query must be at most 50 words.")

    api_key = api_key or os.environ.get("BRAVE_API_KEY")
    if not api_key:
        raise ValueError("Brave API key is required. Provide it or set BRAVE_API_KEY.")

    client = AsyncBrave(api_key=api_key)

    params: Dict[str, Any] = {
        "q": q,
        "country": country,
        "search_lang": search_lang,
        "ui_lang": ui_lang,
        "count": count,
        "offset": offset,
        "safesearch": safesearch,
        "freshness": freshness,
        "text_decorations": int(text_decorations),
        "spellcheck": int(spellcheck),
        "result_filter": result_filter,
        "goggles_id": goggles_id,
        "goggles": goggles,
        "units": units,
        "extra_snippets": int(extra_snippets),
        "summary": int(summary),
    }
    params = {k: v for k, v in params.items() if v is not None}

    async def _run():
        return await client.search(**params)

    return asyncio.get_event_loop().run_until_complete(_run())


@flow(name="Brave Search Flow")
def brave_search_example():
    res = brave_search.submit(
        "openai",
        country="CA",
        count=10,
        safesearch="strict",
        extra_snippets=True
    )
    return res.result()


if __name__ == "__main__":
    brave_search_example()


- `messages`: List of messages (required)
- `model`: Model to use (default 'sonar')
- `max_tokens`: Maximum number of tokens to generate
- `temperature`: Controls randomness (0-2)
- `top_p`: Controls diversity via nucleus sampling (0-1)
- `search_domain_filter`: List of domains to include in search
- `return_images`: Whether to return images
- `return_related_questions`: Whether to return related questions
- `search_recency_filter`: Time range for search results
- `top_k`: Number of results to return
- `stream`: Whether to stream responses
- `presence_penalty`: Penalty for new tokens based on presence in text (0-2)
- `frequency_penalty`: Penalty for new tokens based on frequency in text (0-2)
- `response_format`: Response format options
- `web_search_options`: Options for web search
import os
import requests
import httpx
from typing import Any, Dict, List, Optional
from prefect import flow, task

# Default endpoints and credentials from environment
PERPLEXITY_API_URL = os.getenv(
    "PERPLEXITY_API_URL", "https://api.perplexity.ai/chat/completions"
)
PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY")


@task(name="perplexity_chat")
def perplexity_chat(
    messages: List[Dict[str, str]],
    model: str = "sonar",
    max_tokens: Optional[int] = None,
    temperature: float = 0.2,
    top_p: float = 0.9,
    search_domain_filter: Optional[List[str]] = None,
    return_images: bool = False,
    return_related_questions: bool = False,
    search_recency_filter: Optional[str] = None,
    top_k: int = 0,
    stream: bool = False,
    presence_penalty: float = 0.0,
    frequency_penalty: float = 1.0,
    response_format: Optional[Dict[str, Any]] = None,
    web_search_options: Optional[Dict[str, Any]] = None,
    **kwargs: Any,
) -> Dict[str, Any]:
    """
    Send a chat-completion request to the Perplexity API (synchronous).
    """
    if not PERPLEXITY_API_KEY:
        raise ValueError("PERPLEXITY_API_KEY environment variable not set.")

    payload: Dict[str, Any] = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "top_p": top_p,
        "return_images": return_images,
        "return_related_questions": return_related_questions,
        "top_k": top_k,
        "stream": stream,
        "presence_penalty": presence_penalty,
        "frequency_penalty": frequency_penalty,
    }
    if max_tokens is not None:
        payload["max_tokens"] = max_tokens
    if search_domain_filter:
        payload["search_domain_filter"] = search_domain_filter
    if search_recency_filter:
        payload["search_recency_filter"] = search_recency_filter
    if response_format:
        payload["response_format"] = response_format
    if web_search_options:
        payload["web_search_options"] = web_search_options
    payload.update(kwargs)

    headers = {
        "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
        "Content-Type": "application/json",
    }

    resp = requests.post(PERPLEXITY_API_URL, json=payload, headers=headers)
    resp.raise_for_status()
    return resp.json()


@task(name="perplexity_chat_async")
async def perplexity_chat_async(
    messages: List[Dict[str, str]],
    model: str = "sonar",
    max_tokens: Optional[int] = None,
    temperature: float = 0.2,
    top_p: float = 0.9,
    search_domain_filter: Optional[List[str]] = None,
    return_images: bool = False,
    return_related_questions: bool = False,
    search_recency_filter: Optional[str] = None,
    top_k: int = 0,
    stream: bool = False,
    presence_penalty: float = 0.0,
    frequency_penalty: float = 1.0,
    response_format: Optional[Dict[str, Any]] = None,
    web_search_options: Optional[Dict[str, Any]] = None,
    **kwargs: Any,
) -> Dict[str, Any]:
    """
    Send a chat-completion request to the Perplexity API (asynchronous).
    """
    if not PERPLEXITY_API_KEY:
        raise ValueError("PERPLEXITY_API_KEY environment variable not set.")

    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "top_p": top_p,
        "return_images": return_images,
        "return_related_questions": return_related_questions,
        "top_k": top_k,
        "stream": stream,
        "presence_penalty": presence_penalty,
        "frequency_penalty": frequency_penalty,
    }
    if max_tokens is not None:
        payload["max_tokens"] = max_tokens
    if search_domain_filter:
        payload["search_domain_filter"] = search_domain_filter
    if search_recency_filter:
        payload["search_recency_filter"] = search_recency_filter
    if response_format:
        payload["response_format"] = response_format
    if web_search_options:
        payload["web_search_options"] = web_search_options
    payload.update(kwargs)

    headers = {
        "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
        "Content-Type": "application/json",
    }

    async with httpx.AsyncClient() as client:
        resp = await client.post(PERPLEXITY_API_URL, json=payload, headers=headers)
        resp.raise_for_status()
        return resp.json()


@flow(name="run_perplexity_flow")
def run_perplexity_flow():
    """
    Flow to run the synchronous Perplexity chat task.
    """
    messages = [
        {"role": "system", "content": "Be precise and concise."},
        {"role": "user", "content": "How many stars are there in our galaxy?"}
    ]
    try:
        result = perplexity_chat.submit(messages=messages, model="sonar", max_tokens=100).result()
        print("Response:", result)
    except Exception as e:
        print("Error during Perplexity API call:", e)


@flow(name="run_perplexity_flow_async")
def run_perplexity_flow_async():
    """
    Flow to run the asynchronous Perplexity chat task.
    """
    messages = [
        {"role": "system", "content": "Be precise and concise."},
        {"role": "user", "content": "How many stars are there in our galaxy?"}
    ]
    try:
        result = perplexity_chat_async.submit(messages=messages, model="sonar", max_tokens=100).result()
        print("Async Response:", result)
    except Exception as e:
        print("Async error during Perplexity API call:", e)


if __name__ == "__main__":
    run_perplexity_flow()
    # Uncomment to run async flow:
    # run_perplexity_flow_async()


@flow(name="custom_data_pipeline")
def custom_data_pipeline_flow(
    config_override: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Main data pipeline flow that orchestrates all components.
    
    Args:
        config_override: Optional configuration overrides
        
    Returns:
        Dictionary containing pipeline results
    """
    # Load configuration
    config = load_config()
    if config_override:
        config.update(config_override)
    
    logger.info(f"Starting custom_data_pipeline pipeline")
    results = {}
    
    try:

        # Chunking Phase
        logger.info("Starting chunking phase")
        chunks = chunking_task(loaded_data, config)
        results["chunks"] = chunks
        logger.info(f"Created {len(chunks)} chunks")

        # Embedding Phase
        logger.info("Starting embedding phase")
        embeddings = embedding_task(chunks, config)
        results["embeddings"] = embeddings
        logger.info(f"Generated embeddings for {len(embeddings)} chunks")

        # Vector Store Phase
        logger.info("Starting vector store phase")
        vector_store_result = vector_store_task(chunks, embeddings, config)
        results["vector_store"] = vector_store_result
        logger.info("Data stored in vector database")

        # LLM Setup Phase
        logger.info("Setting up LLM for RAG")
        llm_setup = llm_setup_task(config)
        results["llm_setup"] = llm_setup
        logger.info("LLM configured for retrieval and generation")

        logger.info(f"{pipeline_name} pipeline completed successfully")
        return results
        
    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}")
        raise


# RAG Query Function
@task(name="rag_query_task")
def rag_query_task(query: str, config: Dict[str, Any]) -> str:
    """
    Perform RAG query using the configured pipeline.
    
    Args:
        query: User query
        config: Pipeline configuration
        
    Returns:
        Generated response
    """
    # This function should be customized based on your specific RAG setup
    # It should retrieve relevant chunks and generate a response using the LLM
    logger.info(f"Processing query: {query}")
    
    # Placeholder implementation - customize based on your components
    response = "This is a placeholder response. Customize this function based on your RAG setup."
    
    return response

@flow(name="rag_query_flow")
def rag_query_flow(query: str, config_override: Optional[Dict[str, Any]] = None) -> str:
    """
    Flow for performing RAG queries.
    
    Args:
        query: User query
        config_override: Optional configuration overrides
        
    Returns:
        Generated response
    """
    config = load_config()
    if config_override:
        config.update(config_override)
    
    return rag_query_task(query, config)

if __name__ == "__main__":
    # Example usage
    print("Running custom_data_pipeline pipeline...")
    
    # Run the main pipeline
    result = custom_data_pipeline_flow()
    print("Pipeline completed successfully!")
    print(f"Results: {result}")
    
    # Example RAG query
    query = "What information do you have about the documents?"
    response = rag_query_flow(query)
    print(f"Query: {query}")
    print(f"Response: {response}")

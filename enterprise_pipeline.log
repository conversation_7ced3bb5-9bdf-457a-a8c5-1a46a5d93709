2025-06-02 00:25:19,047 - enterprise_pipeline_system - INFO - Starting enterprise pipeline generation - Session: 6536c6d8-305f-40ea-bf8f-ef8b7a9dece9
2025-06-02 00:25:19,048 - enterprise_pipeline_system - INFO - Executing architecture phase - Session: 6536c6d8-305f-40ea-bf8f-ef8b7a9dece9
2025-06-02 00:25:29,825 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:25:29,831 - enterprise_pipeline_system - INFO - Executing performance optimization phase - Session: 6536c6d8-305f-40ea-bf8f-ef8b7a9dece9
2025-06-02 00:25:42,565 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:25:42,568 - enterprise_pipeline_system - ERROR - Failed to parse performance response: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)
2025-06-02 00:25:53,849 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:27:08,058 - enterprise_pipeline_system - INFO - Starting enterprise pipeline generation - Session: a47304fc-3adf-4e0a-b381-f2605955c23f
2025-06-02 00:27:08,059 - enterprise_pipeline_system - INFO - Executing architecture phase - Session: a47304fc-3adf-4e0a-b381-f2605955c23f
2025-06-02 00:27:25,826 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:27:25,832 - enterprise_pipeline_system - INFO - Executing performance optimization phase - Session: a47304fc-3adf-4e0a-b381-f2605955c23f
2025-06-02 00:27:37,634 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:27:37,636 - enterprise_pipeline_system - ERROR - Failed to parse performance response: Expecting value: line 2 column 18 (char 19)
2025-06-02 00:27:48,406 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:28:10,882 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:28:10,885 - enterprise_pipeline_system - ERROR - Failed to parse devops response: Expecting property name enclosed in double quotes: line 2 column 3 (char 4)
2025-06-02 00:28:19,541 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:28:27,319 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:28:41,471 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:29:04,717 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:29:04,721 - enterprise_pipeline_system - INFO - Assembling enterprise pipeline - Session: a47304fc-3adf-4e0a-b381-f2605955c23f
2025-06-02 00:29:22,081 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:29:22,084 - enterprise_pipeline_system - ERROR - Failed to parse validation response: Extra data: line 1 column 25 (char 24)
2025-06-02 00:29:22,084 - enterprise_pipeline_system - INFO - Generating enterprise deliverables - Session: a47304fc-3adf-4e0a-b381-f2605955c23f
2025-06-02 00:29:22,088 - enterprise_pipeline_system - ERROR - Enterprise pipeline generation failed - Session: a47304fc-3adf-4e0a-b381-f2605955c23f, Error: 'EnterpriseAgentOrchestrator' object has no attribute '_generate_architecture_documentation'
2025-06-02 00:41:40,849 - enterprise_pipeline_system - INFO - Starting collaborative agent phase - Session: f896b0d2-95ba-4375-9f6e-d9fba5c04e82
2025-06-02 00:41:50,130 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:42:01,138 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:42:12,594 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:42:25,434 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:42:35,128 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:42:44,415 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:42:55,455 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:43:06,301 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:43:17,275 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:43:17,279 - enterprise_pipeline_system - INFO - Starting enhanced architecture phase with code integration - Session: f896b0d2-95ba-4375-9f6e-d9fba5c04e82
2025-06-02 00:43:28,668 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:43:28,728 - enterprise_pipeline_system - WARNING - Could not load component code: Section '# Qdrant' not found
2025-06-02 00:43:28,728 - enterprise_pipeline_system - INFO - Executing performance optimization phase - Session: f896b0d2-95ba-4375-9f6e-d9fba5c04e82
2025-06-02 00:43:42,099 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:43:52,566 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:44:13,791 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:44:23,361 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:44:31,314 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:44:46,688 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:44:57,915 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:44:57,919 - enterprise_pipeline_system - ERROR - Enterprise pipeline generation failed - Session: f896b0d2-95ba-4375-9f6e-d9fba5c04e82, Error: unhashable type: 'dict'
2025-06-02 00:54:23,431 - enterprise_pipeline_system - INFO - Starting SUPERIOR enterprise pipeline generation - Session: f7f88e9a-eb66-4f35-9936-9297fb7a6f70
2025-06-02 00:54:23,432 - enterprise_pipeline_system - INFO - Starting collaborative agent phase - Session: f7f88e9a-eb66-4f35-9936-9297fb7a6f70
2025-06-02 00:54:32,373 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:54:43,932 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:54:53,771 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:55:04,698 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:55:14,884 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:55:24,840 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:55:36,955 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:55:47,863 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:55:58,356 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:55:58,358 - enterprise_pipeline_system - INFO - Starting enhanced architecture phase with code integration - Session: f7f88e9a-eb66-4f35-9936-9297fb7a6f70
2025-06-02 00:56:07,837 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:56:07,842 - enterprise_pipeline_system - WARNING - Could not load component code: Section '# Qdrant' not found
2025-06-02 00:56:07,842 - enterprise_pipeline_system - INFO - Executing performance optimization phase - Session: f7f88e9a-eb66-4f35-9936-9297fb7a6f70
2025-06-02 00:56:25,149 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:56:38,469 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:56:58,265 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:57:07,851 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:57:16,559 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:57:27,149 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:57:51,677 - httpx - INFO - HTTP Request: POST https://admins.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2023-05-15 "HTTP/1.1 200 OK"
2025-06-02 00:57:51,680 - enterprise_pipeline_system - INFO - Assembling SUPERIOR enterprise pipeline - Session: f7f88e9a-eb66-4f35-9936-9297fb7a6f70
2025-06-02 00:57:51,680 - enterprise_pipeline_system - ERROR - Enterprise pipeline generation failed - Session: f7f88e9a-eb66-4f35-9936-9297fb7a6f70, Error: unhashable type: 'dict'

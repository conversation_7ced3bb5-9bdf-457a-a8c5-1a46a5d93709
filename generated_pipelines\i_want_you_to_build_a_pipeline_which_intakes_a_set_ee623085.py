"""
Generated Data Pipeline
Created by Multi-Agent Pipeline System
Generated on: 2025-06-02 02:26:40

This pipeline was automatically generated based on your requirements.
Please ensure you have all required API keys and dependencies installed.
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from prefect import task, flow
import json

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Configuration
CONFIG = {
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "max_results": 10,
    "temperature": 0.7,

}

def load_config():
    """Load configuration from environment variables or config file."""
    config = CONFIG.copy()
    
    # Override with environment variables if available
    for key in config:
        env_key = key.upper()
        if env_key in os.environ:
            config[key] = os.environ[env_key]
    
    return config


# Component Functions

# DATA_LOADING COMPONENT
from prefect import task, flow
from typing import List, Dict, Any, Optional, Union, Tuple
import os
import logging
import time
import json
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    logger.warning("PyMuPDF package not available. Install with: pip install pymupdf")
    PYMUPDF_AVAILABLE = False

@task(name="check_pymupdf_dependencies")
def check_pymupdf_dependencies() -> bool:
    """Check if PyMuPDF and its dependencies are installed."""
    if not PYMUPDF_AVAILABLE:
        logger.error("PyMuPDF package is required but not installed.")
        logger.info("Install with: pip install pymupdf")
        return False
    return True

@task(name="extract_pdf_metadata")
def extract_pdf_metadata(pdf_path: str) -> Dict[str, Any]:
    """Extract metadata from a PDF file.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        Dictionary containing PDF metadata
    """
    if not PYMUPDF_AVAILABLE:
        raise ImportError("PyMuPDF package is required but not installed.")

    logger.info(f"Extracting metadata from PDF: {pdf_path}")

    try:
        doc = fitz.open(pdf_path)
        metadata = {
            "title": doc.metadata.get("title", ""),
            "author": doc.metadata.get("author", ""),
            "subject": doc.metadata.get("subject", ""),
            "keywords": doc.metadata.get("keywords", ""),
            "creator": doc.metadata.get("creator", ""),
            "producer": doc.metadata.get("producer", ""),
            "creation_date": doc.metadata.get("creationDate", ""),
            "modification_date": doc.metadata.get("modDate", ""),
            "page_count": len(doc),
            "file_size": os.path.getsize(pdf_path),
            "file_name": os.path.basename(pdf_path)
        }
        doc.close()
        return metadata

    except Exception as e:
        logger.error(f"Error extracting PDF metadata: {e}")
        raise

@task(name="extract_pdf_toc")
def extract_pdf_toc(pdf_path: str) -> List[Dict[str, Any]]:
    """Extract table of contents (TOC) from a PDF file.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        List of TOC entries with title, page, and level
    """
    if not PYMUPDF_AVAILABLE:
        raise ImportError("PyMuPDF package is required but not installed.")

    logger.info(f"Extracting TOC from PDF: {pdf_path}")

    try:
        doc = fitz.open(pdf_path)
        toc = doc.get_toc()

        # Convert TOC to a more usable format
        result = []
        for entry in toc:
            level, title, page = entry
            result.append({
                "level": level,
                "title": title,
                "page": page
            })

        doc.close()
        return result

    except Exception as e:
        logger.error(f"Error extracting PDF TOC: {e}")
        raise

@task(name="extract_pdf_text")
def extract_pdf_text(
    pdf_path: str,
    start_page: int = 0,
    end_page: Optional[int] = None,
    extract_images: bool = False,
    extract_tables: bool = False
) -> List[Dict[str, Any]]:
    """Extract text and optionally images and tables from a PDF file.

    Args:
        pdf_path: Path to the PDF file
        start_page: First page to extract (0-based index)
        end_page: Last page to extract (0-based index, None for all pages)
        extract_images: Whether to extract images
        extract_tables: Whether to attempt table extraction

    Returns:
        List of dictionaries containing page content
    """
    if not PYMUPDF_AVAILABLE:
        raise ImportError("PyMuPDF package is required but not installed.")

    logger.info(f"Extracting text from PDF: {pdf_path}")
    start_time = time.time()

    try:
        doc = fitz.open(pdf_path)

        # Validate page range
        if end_page is None:
            end_page = len(doc) - 1
        else:
            end_page = min(end_page, len(doc) - 1)

        start_page = max(0, start_page)
        if start_page > end_page:
            raise ValueError(f"Invalid page range: {start_page} to {end_page}")

        pages = []
        for page_num in range(start_page, end_page + 1):
            page = doc[page_num]
            page_dict = {
                "page_number": page_num + 1,  # 1-based for user-friendly display
                "text": page.get_text(),
                "images": [],
                "tables": []
            }

            # Extract images if requested
            if extract_images:
                image_list = page.get_images(full=True)
                for img_index, img_info in enumerate(image_list):
                    xref = img_info[0]  # Image reference number
                    try:
                        base_image = doc.extract_image(xref)
                        image_data = {
                            "index": img_index,
                            "width": base_image["width"],
                            "height": base_image["height"],
                            "format": base_image["ext"],
                            # We don't include the binary data in the result
                            # but you could save it to a file here if needed
                        }
                        page_dict["images"].append(image_data)
                    except Exception as e:
                        logger.warning(f"Error extracting image {img_index} on page {page_num + 1}: {e}")

            # Extract tables if requested
            # This is a simplified approach - for better table extraction,
            # consider using specialized libraries like camelot-py or tabula-py
            if extract_tables:
                # Simple heuristic: look for blocks that might be tables
                blocks = page.get_text("blocks")
                for block_index, block in enumerate(blocks):
                    # Check if block contains multiple lines with similar structure
                    # This is a very basic heuristic and won't catch all tables
                    lines = block[4].split('\n')
                    if len(lines) > 3:  # At least 3 rows to be considered a table
                        # Check if lines have similar structure (e.g., same number of spaces or tabs)
                        space_counts = [line.count(' ') for line in lines]
                        tab_counts = [line.count('\t') for line in lines]

                        # If consistent pattern of spaces or tabs, might be a table
                        if (max(space_counts) - min(space_counts) <= 3) or (max(tab_counts) - min(tab_counts) <= 1):
                            table_data = {
                                "index": block_index,
                                "bbox": block[:4],  # Bounding box
                                "text": block[4],
                                "rows": lines
                            }
                            page_dict["tables"].append(table_data)

            pages.append(page_dict)

        doc.close()

        elapsed_time = time.time() - start_time
        logger.info(f"PDF text extraction completed in {elapsed_time:.2f} seconds. Extracted {len(pages)} pages.")

        return pages

    except Exception as e:
        logger.error(f"Error extracting PDF text: {e}")
        raise

@task(name="convert_pdf_to_markdown")
def convert_pdf_to_markdown(
    pages: List[Dict[str, Any]],
    metadata: Dict[str, Any],
    toc: List[Dict[str, Any]]
) -> str:
    """Convert extracted PDF content to markdown format.

    Args:
        pages: List of page dictionaries with extracted content
        metadata: PDF metadata
        toc: Table of contents

    Returns:
        Markdown representation of the PDF
    """
    logger.info("Converting PDF content to markdown")

    markdown = []

    # Add title and metadata
    if metadata.get("title"):
        markdown.append(f"# {metadata['title']}\n")
    else:
        markdown.append(f"# {metadata['file_name']}\n")

    # Add metadata section
    markdown.append("## Document Information\n")
    if metadata.get("author"):
        markdown.append(f"* **Author:** {metadata['author']}\n")
    if metadata.get("creation_date"):
        markdown.append(f"* **Created:** {metadata['creation_date']}\n")
    if metadata.get("modification_date"):
        markdown.append(f"* **Modified:** {metadata['modification_date']}\n")
    markdown.append(f"* **Pages:** {metadata['page_count']}\n")

    # Add table of contents if available
    if toc:
        markdown.append("\n## Table of Contents\n")
        for entry in toc:
            indent = "  " * (entry["level"] - 1)
            markdown.append(f"{indent}* [{entry['title']}](#page-{entry['page']})\n")

    # Add content for each page
    for page in pages:
        page_num = page["page_number"]
        markdown.append(f"\n## Page {page_num}\n")

        # Add page text
        text = page["text"]
        # Clean up text - remove excessive newlines, etc.
        text = "\n".join(line for line in text.split("\n") if line.strip())
        markdown.append(text + "\n")

        # Add image placeholders if any
        if page["images"]:
            markdown.append("\n### Images\n")
            for img in page["images"]:
                markdown.append(f"* Image {img['index']+1}: {img['width']}x{img['height']} {img['format']}\n")

        # Add table content if any
        if page["tables"]:
            markdown.append("\n### Tables\n")
            for table in page["tables"]:
                markdown.append("\n```\n")
                markdown.append(table["text"])
                markdown.append("\n```\n")

    return "".join(markdown)

@task(name="chunk_markdown_content")
def chunk_markdown_content(
    markdown: str,
    chunk_size: int = 1000,
    chunk_overlap: int = 200
) -> List[Dict[str, Any]]:
    """Chunk markdown content into smaller pieces.

    Args:
        markdown: Markdown content to chunk
        chunk_size: Maximum number of characters per chunk
        chunk_overlap: Number of characters to overlap between chunks

    Returns:
        List of chunked content with metadata
    """
    logger.info(f"Chunking markdown content with chunk size: {chunk_size}, overlap: {chunk_overlap}")

    # Split by headers to preserve document structure
    import re
    header_pattern = r"(^#{1,6}\s.+$)"
    sections = re.split(header_pattern, markdown, flags=re.MULTILINE)

    # Pair headers with their content
    chunks = []
    current_chunk = ""
    current_metadata = {}
    current_headers = {}

    for i in range(0, len(sections)):
        section = sections[i]
        if not section.strip():
            continue

        # Check if this is a header
        header_match = re.match(r"^(#{1,6})\s(.+)$", section)
        if header_match:
            level = len(header_match.group(1))
            title = header_match.group(2).strip()

            # Update current headers based on level
            for l in range(level, 7):
                if l in current_headers:
                    del current_headers[l]
            current_headers[level] = title

            # Update metadata with header info
            if level == 1:
                current_metadata["title"] = title
            elif level == 2:
                current_metadata["section"] = title
            elif level == 3:
                current_metadata["subsection"] = title

            # Add header to current chunk
            if len(current_chunk) + len(section) + 1 > chunk_size and current_chunk:
                # Save current chunk and start a new one
                chunks.append({
                    "text": current_chunk,
                    "metadata": current_metadata.copy()
                })

                # Start new chunk with overlap
                if chunk_overlap > 0 and current_chunk:
                    # Get the last part of the previous chunk for overlap
                    overlap_chars = min(chunk_overlap, len(current_chunk))
                    current_chunk = current_chunk[-overlap_chars:]
                else:
                    current_chunk = ""

            current_chunk += section + "\n"
        else:
            # This is content, add it to the current chunk
            # Check if adding this content would exceed chunk size
            if len(current_chunk) + len(section) + 1 > chunk_size and current_chunk:
                # Save current chunk and start a new one
                chunks.append({
                    "text": current_chunk,
                    "metadata": current_metadata.copy()
                })

                # Start new chunk with overlap
                if chunk_overlap > 0 and current_chunk:
                    overlap_chars = min(chunk_overlap, len(current_chunk))
                    current_chunk = current_chunk[-overlap_chars:]
                else:
                    current_chunk = ""

            current_chunk += section + "\n"

    # Add the last chunk if not empty
    if current_chunk:
        chunks.append({
            "text": current_chunk,
            "metadata": current_metadata.copy()
        })

    logger.info(f"Chunking completed. Created {len(chunks)} chunks.")
    return chunks

@flow(name="PyMuPDF4LLM PDF Parsing Flow")
def pymupdf_pdf_parsing_flow(
    pdf_path: str,
    start_page: int = 0,
    end_page: Optional[int] = None,
    extract_images: bool = False,
    extract_tables: bool = False,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    output_format: str = "markdown"
) -> Dict[str, Any]:
    """Prefect flow for parsing PDFs using PyMuPDF.

    Args:
        pdf_path: Path to the PDF file
        start_page: First page to extract (0-based index)
        end_page: Last page to extract (0-based index, None for all pages)
        extract_images: Whether to extract images
        extract_tables: Whether to attempt table extraction
        chunk_size: Maximum number of characters per chunk
        chunk_overlap: Number of characters to overlap between chunks
        output_format: Output format ('markdown' or 'text')

    Returns:
        Dictionary containing the parsed and chunked content
    """
    # Check dependencies
    dependencies_ok = check_pymupdf_dependencies()
    if not dependencies_ok:
        return {"error": "PyMuPDF package is not installed"}

    # Validate PDF path
    if not os.path.exists(pdf_path):
        return {"error": f"PDF file not found: {pdf_path}"}

    # Extract metadata
    metadata = extract_pdf_metadata(pdf_path)

    # Extract table of contents
    toc = extract_pdf_toc(pdf_path)

    # Extract text and other content
    pages = extract_pdf_text(
        pdf_path=pdf_path,
        start_page=start_page,
        end_page=end_page,
        extract_images=extract_images,
        extract_tables=extract_tables
    )

    # Convert to markdown
    markdown_content = convert_pdf_to_markdown(
        pages=pages,
        metadata=metadata,
        toc=toc
    )

    # Chunk the content
    chunked_content = chunk_markdown_content(
        markdown=markdown_content,
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap
    )

    # Prepare result
    result = {
        "pdf_path": pdf_path,
        "metadata": metadata,
        "toc": toc,
        "page_count": len(pages),
        "chunks_count": len(chunked_content),
        "chunks": chunked_content
    }

    # Include full markdown if requested
    if output_format == "markdown":
        result["markdown"] = markdown_content

    return result

# Example usage
if __name__ == "__main__":
    # Example PDF path
    pdf_path = "path/to/your/document.pdf"

    # Run the flow
    result = pymupdf_pdf_parsing_flow(
        pdf_path=pdf_path,
        start_page=0,  # Start from first page
        end_page=None,  # Process all pages
        extract_images=True,
        extract_tables=True,
        chunk_size=1000,
        chunk_overlap=200,
        output_format="markdown"
    )

    # Print summary
    print(f"Processed PDF: {result['pdf_path']}")
    print(f"Title: {result['metadata'].get('title', 'N/A')}")
    print(f"Author: {result['metadata'].get('author', 'N/A')}")
    print(f"Pages: {result['page_count']}")
    print(f"Chunks created: {result['chunks_count']}")

    # Print first chunk as example
    if result['chunks']:
        first_chunk = result['chunks'][0]
        print("\nExample chunk:")
        print(f"Text: {first_chunk['text'][:200]}...")
        print(f"Metadata: {first_chunk['metadata']}")
This implementation provides a comprehensive workflow for parsing PDFs using the PyMuPDF (fitz) library with the following features:
- Fast and efficient PDF text extraction
- Metadata and table of contents extraction
- Support for image and table detection
- Conversion to markdown format with proper structure
- Intelligent chunking with configurable size and overlap
- Page range selection for processing subsets of documents
- Proper error handling and logging
- Wrapped in a Prefect flow for easy integration into data pipelines

# CHUNKING COMPONENT
from prefect import task, flow
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import tiktoken
import logging

# For semantic chunking, we'll need a way to compute embeddings
# This is a dummy implementation - in a real scenario, you would use a proper embedding model
# like OpenAI's text-embedding-ada-002, HuggingFace models, or other embedding services

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@task
def load_document(file_path: str) -> str:
    """Load document content from a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        logger.info(f"Successfully loaded document from {file_path}")
        return content
    except Exception as e:
        logger.error(f"Error loading document: {e}")
        raise

@task
def count_tokens(text: str, encoding_name: str = "cl100k_base") -> int:
    """Count the number of tokens in the text using tiktoken."""
    try:
        encoding = tiktoken.get_encoding(encoding_name)
        tokens = encoding.encode(text)
        return len(tokens)
    except Exception as e:
        logger.error(f"Error counting tokens: {e}")
        # Fallback to approximate counting if tiktoken fails
        return len(text.split()) * 1.3  # Rough approximation

@task
def get_embeddings(texts: List[str], model: str = "dummy-embedding-model") -> List[np.ndarray]:
    """Get embeddings for a list of texts.

    This is a dummy implementation that returns random embeddings.
    In a real scenario, you would use a proper embedding model or API.

    Args:
        texts: List of text strings to embed
        model: Name of the embedding model to use

    Returns:
        List of embedding vectors
    """
    logger.info(f"Getting embeddings for {len(texts)} texts using {model}")

    # In a real implementation, you would call an embedding API or model here
    # For example, with OpenAI:
    # response = openai.Embedding.create(input=texts, model=model)
    # embeddings = [np.array(item["embedding"]) for item in response["data"]]

    # Dummy implementation - returns random embeddings of dimension 384
    embedding_dim = 384
    embeddings = []

    for text in texts:
        # Create a deterministic but random-looking embedding based on the text content
        # This is just for demonstration - not for actual use
        seed = sum(ord(c) for c in text[:100])  # Simple hash of the text
        np.random.seed(seed)
        embedding = np.random.randn(embedding_dim)
        # Normalize the embedding to unit length
        embedding = embedding / np.linalg.norm(embedding)
        embeddings.append(embedding)

    return embeddings

@task
def cosine_similarity(vec1: np.ndarray, vec2: np.ndarray) -> float:
    """Compute cosine similarity between two vectors."""
    return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))

@task
def split_text_into_sentences(text: str) -> List[str]:
    """Split text into sentences using simple rules.

    In a real implementation, you might use a more sophisticated NLP library.
    """
    import re
    # Simple sentence splitting - can be improved with NLP libraries
    sentences = re.split(r'(?<=[.!?])\s+', text)
    return [s.strip() for s in sentences if s.strip()]

@task
def semantic_chunking(
    text: str,
    max_chunk_size: int = 1000,
    min_chunk_size: int = 50,
    similarity_threshold: float = 0.7,
    encoding_name: str = "cl100k_base"
) -> List[Dict[str, Any]]:
    """Split text into chunks based on semantic similarity.

    Args:
        text: The text to split into chunks
        max_chunk_size: Maximum size of a chunk (in tokens)
        min_chunk_size: Minimum size of a chunk (in tokens)
        similarity_threshold: Threshold for determining semantic similarity
        encoding_name: The name of the tiktoken encoding to use

    Returns:
        A list of dictionaries, each containing a chunk of text and metadata
    """
    # Initialize token counter
    try:
        encoding = tiktoken.get_encoding(encoding_name)
        def token_counter(s):
            return len(encoding.encode(s))
    except Exception as e:
        logger.error(f"Error initializing tokenizer: {e}")
        # Fallback to simple word count approximation
        def token_counter(s):
            return len(s.split()) * 1.3  # Rough approximation

    # Split text into sentences
    sentences = split_text_into_sentences(text)
    logger.info(f"Split text into {len(sentences)} sentences")

    if not sentences:
        return []

    # Get embeddings for all sentences
    sentence_embeddings = get_embeddings(sentences)

    # Initialize chunks
    chunks = []
    current_chunk_sentences = [sentences[0]]
    current_chunk_embedding = sentence_embeddings[0].copy()
    current_chunk_tokens = token_counter(sentences[0])

    # Process sentences to form semantically coherent chunks
    for i in range(1, len(sentences)):
        sentence = sentences[i]
        sentence_embedding = sentence_embeddings[i]
        sentence_tokens = token_counter(sentence)

        # Check if adding this sentence would exceed max token limit
        if current_chunk_tokens + sentence_tokens > max_chunk_size:
            # Finalize current chunk
            chunk_text = " ".join(current_chunk_sentences)
            chunks.append({
                "text": chunk_text,
                "metadata": {
                    "token_count": current_chunk_tokens,
                    "sentence_count": len(current_chunk_sentences),
                    "chunk_type": "semantic"
                }
            })

            # Start a new chunk with this sentence
            current_chunk_sentences = [sentence]
            current_chunk_embedding = sentence_embedding.copy()
            current_chunk_tokens = sentence_tokens
            continue

        # Compute similarity between current sentence and current chunk
        # For the chunk embedding, we use a simple average of sentence embeddings
        # In a real implementation, you might use a more sophisticated approach
        avg_chunk_embedding = current_chunk_embedding / len(current_chunk_sentences)
        similarity = cosine_similarity(avg_chunk_embedding, sentence_embedding)

        # If similarity is above threshold, add to current chunk
        if similarity >= similarity_threshold:
            current_chunk_sentences.append(sentence)
            current_chunk_embedding += sentence_embedding
            current_chunk_tokens += sentence_tokens
        else:
            # If current chunk is too small, add this sentence anyway
            if current_chunk_tokens < min_chunk_size:
                current_chunk_sentences.append(sentence)
                current_chunk_embedding += sentence_embedding
                current_chunk_tokens += sentence_tokens
            else:
                # Finalize current chunk and start a new one
                chunk_text = " ".join(current_chunk_sentences)
                chunks.append({
                    "text": chunk_text,
                    "metadata": {
                        "token_count": current_chunk_tokens,
                        "sentence_count": len(current_chunk_sentences),
                        "chunk_type": "semantic"
                    }
                })

                # Start a new chunk with this sentence
                current_chunk_sentences = [sentence]
                current_chunk_embedding = sentence_embedding.copy()
                current_chunk_tokens = sentence_tokens

    # Add the last chunk if it's not empty
    if current_chunk_sentences:
        chunk_text = " ".join(current_chunk_sentences)
        chunks.append({
            "text": chunk_text,
            "metadata": {
                "token_count": current_chunk_tokens,
                "sentence_count": len(current_chunk_sentences),
                "chunk_type": "semantic"
            }
        })

    # Add index information to chunks
    for i, chunk in enumerate(chunks):
        chunk["metadata"]["chunk_index"] = i
        chunk["metadata"]["total_chunks"] = len(chunks)

    logger.info(f"Created {len(chunks)} semantic chunks")
    return chunks

@flow(name="Semantic Chunking Flow")
def semantic_chunking_flow(
    file_path: str,
    max_chunk_size: int = 1000,
    min_chunk_size: int = 50,
    similarity_threshold: float = 0.7
) -> List[Dict[str, Any]]:
    """Prefect flow for semantic chunking of a document.

    Args:
        file_path: Path to the document file
        max_chunk_size: Maximum size of a chunk (in tokens)
        min_chunk_size: Minimum size of a chunk (in tokens)
        similarity_threshold: Threshold for determining semantic similarity
    """
    # Load the document
    document = load_document(file_path)

    # Perform semantic chunking
    chunks = semantic_chunking(
        text=document,
        max_chunk_size=max_chunk_size,
        min_chunk_size=min_chunk_size,
        similarity_threshold=similarity_threshold
    )

    return chunks

# Example usage
if __name__ == "__main__":
    # Example document path
    document_path = "path/to/your/document.txt"

    # Run the flow
    chunks = semantic_chunking_flow(
        file_path=document_path,
        max_chunk_size=1000,
        min_chunk_size=50,
        similarity_threshold=0.7
    )

    # Print the first chunk as an example
    if chunks:
        print(f"First chunk: {chunks[0]['text'][:100]}...")
        print(f"Metadata: {chunks[0]['metadata']}")
        print(f"Total chunks created: {len(chunks)}")
This implementation provides a semantic chunking strategy with the following features:
- Uses embeddings to detect semantic similarity between sentences
- Groups semantically related sentences into coherent chunks
- Respects maximum and minimum chunk size constraints
- Includes a configurable similarity threshold
- Provides detailed metadata for each chunk
- Uses a dummy embedding model (replace with a real one in production)
- Wrapped in a Prefect flow for easy integration into data pipelines

# EMBEDDING COMPONENT
@task(name="calculate_sentence_embeddings")
def calculate_embeddings(sentences, model_name):
    """
    Calculate embeddings for a list of sentences using a SentenceTransformer model.
    Args:
        sentences (list): List of strings to encode
        model_name (str): Name of the SentenceTransformer model to use
    Returns:
        tuple: (embeddings, similarities) - The embeddings array and similarity matrix
    """
    model = SentenceTransformer(model_name)
    embeddings = model.encode(sentences)
    print(f"Embeddings shape: {embeddings.shape}")
    similarities = model.similarity(embeddings, embeddings)
    print("Similarities matrix:")
    print(similarities)
    return embeddings, similarities
@flow(name="sentence_embedding_flow")
def sentence_embedding_flow():
    sentences = [
        "The weather is lovely today.",
        "It's so sunny outside!",
        "He drove to the stadium.",
    ]
    model_name = "nvidia/NV-Embed-v2"  # This parameter is allowed to be altered from the options mentioned
    embeddings, similarities = calculate_embeddings(sentences, model_name)
    return embeddings, similarities
if __name__ == "__main__":
    sentence_embedding_flow()
### Available parameters to alter
model_name:
- "intfloat/multilingual-e5-large-instruct"
- "nvidia/NV-Embed-v2"
- "Alibaba-NLP/gte-Qwen2-7B-instruct"
- "Alibaba-NLP/gte-Qwen2-1.5B-instruct"

# Cohere API
## embed-v4.0
- Embed Text
from prefect import task, flow
import cohere

# VECTOR_STORE COMPONENT

from prefect import flow, task
from pinecone import Pinecone
import os
import time

@task(name="initialize_pinecone")
def initialize_pinecone(api_key: str) -> Pinecone:
    """
    Initialize a Pinecone client with the provided API key.
    """
    pc = Pinecone(api_key=api_key)
    return pc

@task(name="create_index")
def create_index(
    pc: Pinecone,
    index_name: str,
    cloud: str = "aws",
    region: str = "us-east-1",
    model: str = "llama-text-embed-v2",
    field_map: dict = {"text": "chunk_text"}
) -> Pinecone.Index:
    """
    Create a dense index with integrated embedding model.
    """
    if not pc.has_index(index_name):
        pc.create_index_for_model(
            name=index_name,
            cloud=cloud,
            region=region,
            embed={"model": model, "field_map": field_map}
        )
        print(f"Index '{index_name}' created successfully")
    else:
        print(f"Index '{index_name}' already exists")
    return pc.Index(index_name)

@task(name="upsert_data")
def upsert_data(
    index: Pinecone.Index,
    namespace: str,
    records: list,
    wait_time: int = 10
) -> dict:
    """
    Upsert data records into the specified namespace.
    """
    index.upsert_records(namespace, records)
    print(f"Upserted {len(records)} records to namespace '{namespace}'")
    print(f"Waiting {wait_time} seconds for indexing to complete...")
    time.sleep(wait_time)
    stats = index.describe_index_stats()
    print("Index stats:")
    print(f"- Total vector count: {stats['total_vector_count']}")
    print(f"- Namespace '{namespace}' vector count: {stats['namespaces'].get(namespace, {{}}).get('vector_count', 0)}")
    return stats

@task(name="semantic_search")
def semantic_search(
    index: Pinecone.Index,
    namespace: str,
    query_text: str,
    top_k: int = 10,
    print_results: bool = True
) -> dict:
    """
    Perform semantic search on the index.
    """
    results = index.search(
        namespace=namespace,
        query={"top_k": top_k, "inputs": {'text': query_text}}
    )
    if print_results:
        print(f"\nSearch results for: '{query_text}'")
        for hit in results['result']['hits']:
            print(
                f"id: {hit['_id']:<5} | score: {round(hit['_score'], 2):<5} | "
                f"category: {hit['fields']['category']:<10} | "
                f"text: {hit['fields']['chunk_text']:<50}"
            )
    return results

@task(name="rerank_results")
def rerank_results(
    index: Pinecone.Index,
    namespace: str,
    query_text: str,
    top_k: int = 10,
    rerank_model: str = "bge-reranker-v2-m3",
    top_n: int = 10,
    rank_fields: list = ["chunk_text"],
    print_results: bool = True
) -> dict:
    """
    Perform semantic search with reranking on the index.
    """
    reranked = index.search(
        namespace=namespace,
        query={"top_k": top_k, "inputs": {'text': query_text}},
        rerank={"model": rerank_model, "top_n": top_n, "rank_fields": rank_fields}
    )
    if print_results:
        print(f"\nReranked results for: '{query_text}'")
        for hit in reranked['result']['hits']:
            print(
                f"id: {hit['_id']:<5} | score: {round(hit['_score'], 2):<5} | "
                f"category: {hit['fields']['category']:<10} | "
                f"text: {hit['fields']['chunk_text']:<50}"
            )
    return reranked

@task(name="delete_index")
def delete_index(pc: Pinecone, index_name: str) -> None:
    """
    Delete an index.
    """
    pc.delete_index(index_name)
    print(f"Index '{index_name}' deleted successfully")

@flow(name="add_documents_flow")
def add_documents_flow(
    api_key: str,
    index_name: str,
    namespace: str,
    documents: list,
    cloud: str = "aws",
    region: str = "us-east-1",
    model: str = "llama-text-embed-v2",
    field_map: dict = {"text": "chunk_text"},
    wait_time: int = 10
) -> dict:
    """
    Complete flow for adding documents to Pinecone.
    """
    pc = initialize_pinecone(api_key)
    index = create_index(pc, index_name, cloud, region, model, field_map)
    stats = upsert_data(index, namespace, documents, wait_time)
    return {"pc": pc, "index": index, "stats": stats}

@flow(name="search_documents_flow")
def search_documents_flow(
    api_key: str,
    index_name: str,
    namespace: str,
    query_text: str,
    top_k: int = 10,
    use_reranking: bool = False,
    rerank_model: str = "bge-reranker-v2-m3",
    top_n: int = 10,
    rank_fields: list = ["chunk_text"]
) -> dict:
    """
    Complete flow for searching documents in Pinecone.
    """
    pc = initialize_pinecone(api_key)
    index = pc.Index(index_name)
    if use_reranking:
        results = rerank_results(index, namespace, query_text, top_k, rerank_model, top_n, rank_fields)
    else:
        results = semantic_search(index, namespace, query_text, top_k)
    return {"pc": pc, "index": index, "results": results}

if __name__ == "__main__":
    # Replace with your actual API key
    API_KEY = "you-api-key" 
    
    # Define index and namespace names
    INDEX_NAME = "quickstart-demo"
    NAMESPACE = "example-namespace"
    
    # Example documents - in a real application, these would come from your data source
    sample_documents = [
        { "_id": "rec1", "chunk_text": "The Eiffel Tower was completed in 1889 and stands in Paris, France.", "category": "history" },
        { "_id": "rec2", "chunk_text": "Photosynthesis allows plants to convert sunlight into energy.", "category": "science" },
        { "_id": "rec3", "chunk_text": "Albert Einstein developed the theory of relativity.", "category": "science" },
        { "_id": "rec4", "chunk_text": "The mitochondrion is often called the powerhouse of the cell.", "category": "biology" },
        { "_id": "rec5", "chunk_text": "Shakespeare wrote many famous plays, including Hamlet and Macbeth.", "category": "literature" },
        { "_id": "rec6", "chunk_text": "Water boils at 100°C under standard atmospheric pressure.", "category": "physics" },
        { "_id": "rec7", "chunk_text": "The Great Wall of China was built to protect against invasions.", "category": "history" },
        { "_id": "rec8", "chunk_text": "Honey never spoils due to its low moisture content and acidity.", "category": "food science" },
        { "_id": "rec9", "chunk_text": "The speed of light in a vacuum is approximately 299,792 km/s.", "category": "physics" },
        { "_id": "rec10", "chunk_text": "Newton's laws describe the motion of objects.", "category": "physics" },
    ]
    
    # Option 1: Add documents to Pinecone
    add_result = add_documents_flow(
        api_key=API_KEY,
        index_name=INDEX_NAME,
        namespace=NAMESPACE,
        documents=sample_documents,
        wait_time=5  # Reduced wait time for testing
    )
    
    # Option 2: Search documents in Pinecone
    print("\nRunning search_documents_flow...")

    search_result = search_documents_flow(
        api_key=API_KEY,
        index_name=INDEX_NAME,
        namespace=NAMESPACE,
        query_text="Famous historical structures and monuments",
        top_k=5
    )
    
    # Option 3: Search with reranking
    print("\nRunning search_documents_flow with reranking...")
    reranked_search_result = search_documents_flow(
        api_key=API_KEY,
        index_name=INDEX_NAME,
        namespace=NAMESPACE,
        query_text="Famous historical structures and monuments",
        top_k=10,
        use_reranking=True,
        top_n=5
    )
    


import numpy as np
import uuid
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.models import Distance, VectorParams, PointStruct
from prefect import task, flow

# Optional imports for different embedding methods
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

@task(name="initialize_qdrant")
def initialize_qdrant(url=None, api_key=None, local=False):
    """
    Initialize a Qdrant client with various connection options.
    
    Args:
        url (str, optional): URL to Qdrant server or cloud
        api_key (str, optional): API key for Qdrant cloud
        local (bool): Whether to use local mode
        
    Returns:
        QdrantClient: The initialized Qdrant client
    """
    if local:
        return QdrantClient(path=":memory:")
    else:
        return QdrantClient(url=url, api_key=api_key)

@task(name="create_collection")
def create_collection(client, collection_name, vector_size=1536, distance="cosine"):
    """
    Create a new vector collection in Qdrant.
    
    Args:
        client (QdrantClient): The Qdrant client
        collection_name (str): Name for the new collection
        vector_size (int): Dimensionality of vectors
        distance (str): Distance metric ("cosine", "euclid", or "dot")
        
    Returns:
        bool: True if collection was created
    """
    
    # Check if collection already exists
    collections = client.get_collections().collections
    collection_names = [collection.name for collection in collections]
    
    if collection_name in collection_names:
        print(f"Collection '{collection_name}' already exists")
        return False
    
    # Create collection    
    client.create_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(
            size=vector_size, 
            distance=Distance.COSINE
        )
    )
    
    print(f"Collection '{collection_name}' created successfully")
    return True

@task(name="get_embeddings")
def get_embeddings(text, embedding_type="openai", model=None):
    """
    Get embeddings using various embedding models.
    
    Args:
        text (str or list): Text to embed (string or list of strings)
        embedding_type (str): Type of embedding to use
                             "openai" - OpenAI API (requires API key)
                             "sentence_transformers" - HuggingFace models
                             "fastembed" - Use Qdrant's FastEmbed (handled separately)
        model (str): Model name to use (depends on embedding_type)
                    For openai: "text-embedding-ada-002" etc.
                    For sentence_transformers: "all-MiniLM-L6-v2", "all-mpnet-base-v2", etc.
        
    Returns:
        list or ndarray: Embedding vector
    """
    if isinstance(text, list) and embedding_type != "fastembed":
        return [get_embeddings(t, embedding_type, model) for t in text]
    
    if embedding_type == "openai":
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI package not installed. Install with 'pip install openai'")
        
        model = model or "text-embedding-ada-002"
        response = openai.Embedding.create(
            input=text,
            model=model
        )
        return response['data'][0]['embedding']
    
    elif embedding_type == "sentence_transformers":
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("Sentence Transformers not installed. Install with 'pip install sentence-transformers'")
        
        model_name = model or "all-MiniLM-L6-v2"  # Default to a good general purpose model
        model = SentenceTransformer(model_name)
        
        # Get embeddings
        embedding = model.encode(text)
        return embedding.tolist() if isinstance(embedding, np.ndarray) else embedding
    
    else:
        raise ValueError(f"Unsupported embedding type: {embedding_type}")

@task(name="setup_fastembed")
def setup_fastembed(client, model_name="BAAI/bge-base-en", gpu=False):
    """
    Set up FastEmbed for the client. This is Qdrant's built-in embedding feature.
    
    Args:
        client (QdrantClient): The Qdrant client
        model_name (str): Name of the embedding model to use
        gpu (bool): Whether to use GPU for embeddings
        
    Returns:
        QdrantClient: The client with FastEmbed configured
    """
    if gpu:
        client.set_model(
            model_name,
            providers=["CUDAExecutionProvider", "CPUExecutionProvider"]
        )
    else:
        client.set_model(model_name)
    
    print(f"FastEmbed initialized with model: {model_name}")
    return client

@task(name="upsert_chunks")
def upsert_chunks(client, collection_name, chunks, embedding_type="openai", embedding_model=None):
    """
    Embed chunks and upsert into Qdrant collection.
    
    Args:
        client (QdrantClient): The Qdrant client
        collection_name (str): Name of the collection
        chunks (list): List of text chunks to embed and store
        embedding_type (str): Type of embedding to use ("openai", "sentence_transformers", or "fastembed")
        embedding_model (str): Model name to use
        
    Returns:
        dict: Operation result
    """
    points = []
    
    if embedding_type == "fastembed":
        # For FastEmbed, we'll let Qdrant handle the embedding
        # First, ensure FastEmbed is set up
        setup_fastembed(client, model_name=embedding_model or "BAAI/bge-base-en")
        
        # For FastEmbed, we'll use the client.add method
        result = client.add(
            collection_name=collection_name,
            documents=chunks,
            metadata=[{"index": i} for i in range(len(chunks))],
            ids=[str(uuid.uuid4()) for _ in range(len(chunks))]
        )
        
        print(f"Added {len(chunks)} chunks to the collection using FastEmbed")
        return result
    
    else:
        # For other embedding types, we'll handle the embedding ourselves
        for chunk in chunks:
            # Generate embedding
            embedding = get_embeddings(chunk, embedding_type=embedding_type, model=embedding_model)
            
            # Create a unique ID
            point_id = str(uuid.uuid4())
            
            # Create point
            points.append(
                PointStruct(
                    id=point_id,
                    vector=embedding,
                    payload={"text": chunk}
                )
            )
        
        # Upsert points
        result = client.upsert(
            collection_name=collection_name,
            points=points,
            wait=True
        )
        
        print(f"Added {len(chunks)} chunks to the collection using {embedding_type}")
        return result

@task(name="semantic_search")
def semantic_search(client, collection_name, query, embedding_type="openai", embedding_model=None, limit=3):
    """
    Perform semantic search on the collection.
    
    Args:
        client (QdrantClient): The Qdrant client
        collection_name (str): Name of the collection
        query (str): Search query
        embedding_type (str): Type of embedding to use ("openai", "sentence_transformers", or "fastembed")
        embedding_model (str): Model name to use
        limit (int): Number of results to return
        
    Returns:
        list: Search results
    """
    if embedding_type == "fastembed":
        # For FastEmbed, we use the client.query method
        search_results = client.search(
            collection_name=collection_name,
            query_text=query,
            limit=limit
        )
    else:
        # Get query embedding
        query_embedding = get_embeddings(query, embedding_type=embedding_type, model=embedding_model)
        
        # Search the collection
        search_results = client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=limit
        )
    
    return search_results

@flow(name="main")
def main():
    # Cloud Qdrant configuration
    QDRANT_URL = "https://aa97a128-a25f-4bf2-ac0a-e9863ebe178b.us-west-2-0.aws.cloud.qdrant.io:6333"  # Replace with your Qdrant cloud URL
    QDRANT_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.IAgacZmccrWyeZOdeUVI_JGkZ-5IVi8DE6H3PKDvNd0"  # Replace with your Qdrant API key
    
    # Choose your embedding method
    EMBEDDING_TYPE = "sentence_transformers"  # Options: "openai", "sentence_transformers", "fastembed"
    EMBEDDING_MODEL = "all-MiniLM-L6-v2"      # Model name depends on the embedding type
    
    # if EMBEDDING_TYPE == "sentence_transformers":
    #     EMBEDDING_MODEL = "BAAI/bge-base-en"  # Sentence Transformers model

    # OpenAI-specific configuration (only needed if using OpenAI embeddings)
    if EMBEDDING_TYPE == "openai":
        openai.api_key = "your-openai-api-key"  # Replace with your actual API key
        EMBEDDING_MODEL = "text-embedding-ada-002"  # OpenAI model
    
    if EMBEDDING_TYPE == "fastembed":
        EMBEDDING_MODEL = "thenlper/gte-large"  # FastEmbed model

    # Initialize Qdrant client (cloud mode)
    client = initialize_qdrant(url=QDRANT_URL, api_key=QDRANT_API_KEY, local=False)
    print(f"Initialized Qdrant client connected to: {QDRANT_URL}")
    
    # Collection name
    collection_name = "sample-collection8"
    
    # Vector size depends on the embedding model
    vector_size_map = {
        "openai": {
            "text-embedding-ada-002": 1536
        },
        "sentence_transformers": {
            "all-MiniLM-L6-v2": 384,
            "all-mpnet-base-v2": 768,
            "all-distilroberta-v1": 768,
            "paraphrase-multilingual-MiniLM-L12-v2": 768
        },
        "fastembed": {
            "BAAI/bge-base-en": 768,
            "BAAI/bge-small-en": 768,
            "thenlper/gte-large": 768,
        }
    }
    
    vector_size = vector_size_map[EMBEDDING_TYPE].get(EMBEDDING_MODEL, 768)
    
    create_collection(
        client,
        collection_name,
        vector_size=vector_size,
    )
    
    # Sample chunks - replace with your own sample text
    sample_chunks = [
        "The Eiffel Tower was completed in 1889 and stands in Paris, France.",
        "Photosynthesis allows plants to convert sunlight into energy.",
        "Albert Einstein developed the theory of relativity.",
        "The mitochondrion is often called the powerhouse of the cell.",
        "Shakespeare wrote many famous plays, including Hamlet and Macbeth.",
        "Water boils at 100°C under standard atmospheric pressure.",
        "The Great Wall of China was built to protect against invasions.",
        "Honey never spoils due to its low moisture content and acidity.",
        "The speed of light in a vacuum is approximately 299,792 km/s.",
        "Newton's laws describe the motion of objects."
    ]
    
    # Upload chunks to Qdrant
    upsert_chunks(
        client, 
        collection_name, 
        sample_chunks,
        embedding_type=EMBEDDING_TYPE,
        embedding_model=EMBEDDING_MODEL
    )
    
    # Example search query
    query = "Famous historical structures and monuments"
    print(f"\nSearching for: '{query}'")
    
    # Perform search
    results = semantic_search(
        client, 
        collection_name, 
        query,
        embedding_type=EMBEDDING_TYPE,
        embedding_model=EMBEDDING_MODEL
    )
    
    # Display results
    print("\nSearch results:")
    for i, result in enumerate(results):
        print(f"{i+1}. Score: {result.score:.4f}")
        print(f"   Text: {result.payload['text']}")
        print()

if __name__ == "__main__":
    main()


# LLM COMPONENT
@task(name="generate_azure_completions")
def generate_completions(
    messages: List[Dict[str, str]], 
    model_name: str,
    temperature: Optional[float] = None,
    top_p: Optional[float] = None,
    n: Optional[int] = None,
    stream: Optional[bool] = None,
    max_tokens: Optional[int] = None,
    echo: Optional[bool] = None,
    presence_penalty: Optional[float] = None,
    frequency_penalty: Optional[float] = None,
    logit_bias: Optional[Dict[str, float]] = None,
    logprobs: Optional[int] = None,
    user: Optional[str] = None,
    stop: Optional[Union[str, List[str]]] = None,
    seed: Optional[int] = None,
    suffix: Optional[str] = None,
    azure_endpoint: Optional[str] = None,
    api_key: Optional[str] = None,
    api_version: Optional[str] = None
):
    """
    Generate completions using Azure OpenAI API.
    Args:
        messages: List of message dictionaries with 'role' and 'content' (REQUIRED)
        model_name: Name of the deployment model to use (REQUIRED)
        temperature: Controls randomness (0-2)
        top_p: Controls diversity via nucleus sampling (0-1)
        n: Number of completions to generate
        stream: Whether to stream responses
        max_tokens: Maximum number of tokens to generate
        presence_penalty: Penalty for new tokens based on presence in text (0-2)
        frequency_penalty: Penalty for new tokens based on frequency in text (0-2)
        logit_bias: Bias for specific tokens
        logprobs: Number of logprobs to return
        user: Unique user identifier
        stop: Sequences where API will stop generating
        seed: Random number generator seed
        azure_endpoint: Azure OpenAI endpoint URL
        api_key: Azure OpenAI API key
        api_version: API version to use
    Returns:
        The completion text
    """
    client = AzureOpenAI(
        azure_endpoint=azure_endpoint,
        api_key=api_key,
        api_version=api_version
    )
    optional_params = {
        "temperature": temperature,
        "top_p": top_p,
        "n": n,
        "stream": stream,
        "max_tokens": max_tokens,
        "echo": echo,
        "presence_penalty": presence_penalty,
        "frequency_penalty": frequency_penalty,
        "logit_bias": logit_bias,
        "logprobs": logprobs,
        "user": user,
        "stop": stop,
        "seed": seed,
        "suffix": suffix,
    }
    completion_params = {
        "model": model_name,
        "messages": messages,
    }
    completion_params.update({k: v for k, v in optional_params.items() if v is not None})
    response = client.chat.completions.create(**completion_params)
    completion = response.choices[0].message.content
    return completion
@flow(name="azure_openai_flow")
def azure_openai_flow(
    system_message: str,
    user_message: str,
    model_deployment: Optional[str] = None,
    azure_endpoint: Optional[str] = None,
    api_key: Optional[str] = None,
    api_version: Optional[str] = None
    **kwargs
):
    messages = [
        {"role": "system", "content": system_message},
        {"role": "user", "content": user_message}
    ]
    if system_message is None:
        raise ValueError("System message is required")
    if user_message is None:
        raise ValueError("User message is required")
    completion = generate_completions(messages=messages, model_name=model_deployment,azure_endpoint=azure_endpoint,api_key=api_key,api_version=api_version)
    return completion
if __name__ == "__main__":
    azure_openai_flow()
- `model_id`: The Bedrock model ID to use (e.g., 'anthropic.claude-3-sonnet-20240229-v1:0', etc.)
- `region_name`: AWS region name (e.g., 'us-east-1')
- `aws_access_key_id`: AWS access key ID (string)
- `aws_secret_access_key`: AWS secret access key (string)
- `temperature`: Controls randomness (0-1)
- `top_p`: Controls diversity via nucleus sampling (0-1)
- `top_k`: Number of highest probability tokens to consider (int)
- `max_tokens`: Maximum number of tokens to generate (int)
- `stop_sequences`: List of sequences where the model will stop generating (list of strings)
- `anthropic_version`: Version of the Anthropic API to use (string)
- `system_message`: The system message for Claude (string, optional)
- `user_message`: The user message to complete (string)
import os
import json
import boto3
from prefect import task, flow
from typing import List, Dict, Optional, Union, Any



@flow(name="custom_data_pipeline")
def custom_data_pipeline_flow(
    config_override: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Main data pipeline flow that orchestrates all components.
    
    Args:
        config_override: Optional configuration overrides
        
    Returns:
        Dictionary containing pipeline results
    """
    # Load configuration
    config = load_config()
    if config_override:
        config.update(config_override)
    
    logger.info(f"Starting custom_data_pipeline pipeline")
    results = {}
    
    try:

        # Data Loading Phase
        logger.info("Starting data loading phase")
        loaded_data = data_loading_task(config)
        results["loaded_data"] = loaded_data
        logger.info(f"Loaded {len(loaded_data)} documents")

        # Chunking Phase
        logger.info("Starting chunking phase")
        chunks = chunking_task(loaded_data, config)
        results["chunks"] = chunks
        logger.info(f"Created {len(chunks)} chunks")

        # Embedding Phase
        logger.info("Starting embedding phase")
        embeddings = embedding_task(chunks, config)
        results["embeddings"] = embeddings
        logger.info(f"Generated embeddings for {len(embeddings)} chunks")

        # Vector Store Phase
        logger.info("Starting vector store phase")
        vector_store_result = vector_store_task(chunks, embeddings, config)
        results["vector_store"] = vector_store_result
        logger.info("Data stored in vector database")

        # LLM Setup Phase
        logger.info("Setting up LLM for RAG")
        llm_setup = llm_setup_task(config)
        results["llm_setup"] = llm_setup
        logger.info("LLM configured for retrieval and generation")

        logger.info(f"{pipeline_name} pipeline completed successfully")
        return results
        
    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}")
        raise


# RAG Query Function
@task(name="rag_query_task")
def rag_query_task(query: str, config: Dict[str, Any]) -> str:
    """
    Perform RAG query using the configured pipeline.
    
    Args:
        query: User query
        config: Pipeline configuration
        
    Returns:
        Generated response
    """
    # This function should be customized based on your specific RAG setup
    # It should retrieve relevant chunks and generate a response using the LLM
    logger.info(f"Processing query: {query}")
    
    # Placeholder implementation - customize based on your components
    response = "This is a placeholder response. Customize this function based on your RAG setup."
    
    return response

@flow(name="rag_query_flow")
def rag_query_flow(query: str, config_override: Optional[Dict[str, Any]] = None) -> str:
    """
    Flow for performing RAG queries.
    
    Args:
        query: User query
        config_override: Optional configuration overrides
        
    Returns:
        Generated response
    """
    config = load_config()
    if config_override:
        config.update(config_override)
    
    return rag_query_task(query, config)

if __name__ == "__main__":
    # Example usage
    print("Running custom_data_pipeline pipeline...")
    
    # Run the main pipeline
    result = custom_data_pipeline_flow()
    print("Pipeline completed successfully!")
    print(f"Results: {result}")
    
    # Example RAG query
    query = "What information do you have about the documents?"
    response = rag_query_flow(query)
    print(f"Query: {query}")
    print(f"Response: {response}")

# Configuration Template
# Fill in your API keys and configuration values

# Pinecone vector database API key
PINECONE_API_KEY=your_pinecone_api_key_here

# OpenAI API key for GPT models and embeddings
OPENAI_API_KEY=your_openai_api_key_here

# Azure OpenAI API key
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here

# Azure OpenAI endpoint URL
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here


# Additional Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_RESULTS=10
TEMPERATURE=0.7

# Instructions:
# 1. Replace the placeholder values with your actual API keys
# 2. Save this file as .env in your project directory
# 3. Install python-dotenv: pip install python-dotenv
# 4. Load environment variables in your code: from dotenv import load_dotenv; load_dotenv()
